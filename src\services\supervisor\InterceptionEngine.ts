import * as vscode from "vscode"
import { ProcessDetector, ProcessAnalysis, ProcessCategory } from "./ProcessDetector"
import type { SupervisorDecision } from "./AutonomousSupervisor"

export interface InterceptionResult {
	action: "allow" | "block" | "modify" | "prompt"
	originalCommand: string
	modifiedCommand?: string
	reason: string
	riskLevel: "low" | "medium" | "high" | "critical"
	userPromptMessage?: string
	safeAlternatives: string[]
	bypassCode?: string // For emergency overrides
}

export interface InterceptionConfig {
	strictMode: boolean
	allowUserOverrides: boolean
	promptOnMediumRisk: boolean
	autoGenerateAlternatives: boolean
	emergencyBypassEnabled: boolean
	maxBypassesPerSession: number
}

/**
 * Command Interception and Refusal Engine
 * 
 * Provides intelligent interception of dangerous commands with:
 * - Safe alternative generation
 * - User override mechanisms
 * - Emergency bypass codes
 * - Contextual refusal messages
 */
export class InterceptionEngine {
	private detector: ProcessDetector
	private config: InterceptionConfig
	private bypassCount = 0
	private sessionBypassCodes = new Set<string>()
	private outputChannel: vscode.OutputChannel

	constructor(outputChannel: vscode.OutputChannel, config?: Partial<InterceptionConfig>) {
		this.detector = new ProcessDetector()
		this.outputChannel = outputChannel
		this.config = {
			strictMode: true,
			allowUserOverrides: true,
			promptOnMediumRisk: false,
			autoGenerateAlternatives: true,
			emergencyBypassEnabled: true,
			maxBypassesPerSession: 3,
			...config
		}
	}

	/**
	 * Intercept and analyze a command for potential risks
	 */
	public async interceptCommand(
		command: string, 
		workingDir?: string, 
		bypassCode?: string
	): Promise<InterceptionResult> {
		// Check for bypass code first
		if (bypassCode && this.isValidBypassCode(bypassCode)) {
			this.bypassCount++
			this.sessionBypassCodes.add(bypassCode)
			this.log(`Command bypassed with code: ${command}`, "warn")
			
			return {
				action: "allow",
				originalCommand: command,
				reason: "Emergency bypass code used",
				riskLevel: "low",
				safeAlternatives: []
			}
		}

		// Analyze the command
		const analysis = this.detector.analyzeCommand(command, workingDir)
		
		// Generate interception result based on analysis
		const result = await this.generateInterceptionResult(command, analysis, workingDir)
		
		// Log the decision
		this.logInterception(result)
		
		return result
	}

	private async generateInterceptionResult(
		command: string, 
		analysis: ProcessAnalysis, 
		workingDir?: string
	): Promise<InterceptionResult> {
		const safeAlternatives = this.generateSafeAlternatives(command, analysis)
		
		// Determine action based on risk level and configuration
		if (analysis.riskLevel === "critical" || (analysis.riskLevel === "high" && this.config.strictMode)) {
			return this.createBlockResult(command, analysis, safeAlternatives)
		}
		
		if (analysis.riskLevel === "high" || (analysis.riskLevel === "medium" && this.config.promptOnMediumRisk)) {
			if (this.config.allowUserOverrides) {
				return this.createPromptResult(command, analysis, safeAlternatives)
			} else {
				return this.createBlockResult(command, analysis, safeAlternatives)
			}
		}
		
		if (analysis.riskLevel === "medium" && analysis.isUnbounded) {
			return this.createModifyResult(command, analysis, safeAlternatives)
		}
		
		// Low risk or safe commands
		return {
			action: "allow",
			originalCommand: command,
			reason: "Command appears safe",
			riskLevel: analysis.riskLevel,
			safeAlternatives: []
		}
	}

	private createBlockResult(
		command: string, 
		analysis: ProcessAnalysis, 
		safeAlternatives: string[]
	): InterceptionResult {
		const reason = this.generateRefusalMessage(command, analysis)
		const bypassCode = this.config.emergencyBypassEnabled && this.bypassCount < this.config.maxBypassesPerSession
			? this.generateBypassCode()
			: undefined

		return {
			action: "block",
			originalCommand: command,
			reason,
			riskLevel: analysis.riskLevel,
			safeAlternatives,
			bypassCode
		}
	}

	private createPromptResult(
		command: string, 
		analysis: ProcessAnalysis, 
		safeAlternatives: string[]
	): InterceptionResult {
		const promptMessage = this.generatePromptMessage(command, analysis, safeAlternatives)
		
		return {
			action: "prompt",
			originalCommand: command,
			reason: `Command requires user confirmation due to ${analysis.riskLevel} risk`,
			riskLevel: analysis.riskLevel,
			safeAlternatives,
			userPromptMessage: promptMessage
		}
	}

	private createModifyResult(
		command: string, 
		analysis: ProcessAnalysis, 
		safeAlternatives: string[]
	): InterceptionResult {
		const modifiedCommand = this.generateModifiedCommand(command, analysis)
		
		return {
			action: "modify",
			originalCommand: command,
			modifiedCommand,
			reason: `Command modified to prevent unbounded execution: ${analysis.reasons.join(", ")}`,
			riskLevel: analysis.riskLevel,
			safeAlternatives
		}
	}

	private generateSafeAlternatives(command: string, analysis: ProcessAnalysis): string[] {
		const alternatives: string[] = []
		
		// Use suggested alternative from analysis if available
		if (analysis.suggestedAlternative) {
			alternatives.push(analysis.suggestedAlternative)
		}
		
		if (!this.config.autoGenerateAlternatives) {
			return alternatives
		}

		// Generate category-specific alternatives
		switch (analysis.category) {
			case ProcessCategory.SERVER:
				alternatives.push(
					"# Build the project instead:",
					"npm run build",
					"",
					"# Run tests:",
					"npm test",
					"",
					"# Check if server is already running:",
					"curl http://localhost:3000 || echo 'Server not running'"
				)
				break

			case ProcessCategory.WATCHER:
				alternatives.push(
					"# Run build once instead of watching:",
					command.replace(/--watch|-w/g, "").trim(),
					"",
					"# Check current status:",
					"ls -la"
				)
				break

			case ProcessCategory.DATABASE:
				alternatives.push(
					"# Check if database is running:",
					"ps aux | grep -i database",
					"",
					"# Use Docker container instead:",
					"docker run -d --name temp-db database:latest"
				)
				break

			case ProcessCategory.INTERACTIVE:
				alternatives.push(
					"# View file content instead:",
					`cat ${this.extractFilename(command)}`,
					"",
					"# Edit with inline commands:",
					`sed -i 's/old/new/g' ${this.extractFilename(command)}`
				)
				break

			case ProcessCategory.NETWORK:
				alternatives.push(
					"# Make a single request instead:",
					"curl -I http://example.com",
					"",
					"# Test connectivity:",
					"ping -c 4 example.com"
				)
				break

			default:
				alternatives.push(
					"# Consider running a safer version of this command",
					"# Or break it down into smaller, time-limited operations"
				)
		}

		return alternatives
	}

	private generateRefusalMessage(command: string, analysis: ProcessAnalysis): string {
		const baseMessage = `🚫 **Command Blocked by Supervisor**\n\n`
		const riskMessage = `**Risk Level:** ${analysis.riskLevel.toUpperCase()}\n`
		const reasonMessage = `**Reason:** ${analysis.reasons.join(", ")}\n\n`
		
		let categoryMessage = ""
		switch (analysis.category) {
			case ProcessCategory.SERVER:
				categoryMessage = "This command would start a long-running server that could make the session unresponsive. "
				break
			case ProcessCategory.DAEMON:
				categoryMessage = "This command would start a background daemon process. "
				break
			case ProcessCategory.WATCHER:
				categoryMessage = "This command would start a file watcher that runs indefinitely. "
				break
			case ProcessCategory.DATABASE:
				categoryMessage = "This command would start a database server. "
				break
			case ProcessCategory.INTERACTIVE:
				categoryMessage = "This command would start an interactive session. "
				break
			case ProcessCategory.NETWORK:
				categoryMessage = "This command would start a network listener. "
				break
			default:
				categoryMessage = "This command appears to run indefinitely. "
		}

		const explanationMessage = `${categoryMessage}The supervisor prevents such commands to maintain session responsiveness.\n\n`
		
		const helpMessage = `💡 **What you can do:**\n` +
			`• Use the suggested alternatives below\n` +
			`• Break the task into smaller, time-limited operations\n` +
			`• Use existing services instead of starting new ones\n`

		let bypassMessage = ""
		if (this.config.emergencyBypassEnabled && this.bypassCount < this.config.maxBypassesPerSession) {
			bypassMessage = `\n🔓 **Emergency Override:** If absolutely necessary, you can bypass this protection, but use with extreme caution.`
		}

		return baseMessage + riskMessage + reasonMessage + explanationMessage + helpMessage + bypassMessage
	}

	private generatePromptMessage(
		command: string, 
		analysis: ProcessAnalysis, 
		safeAlternatives: string[]
	): string {
		return `⚠️ **Command Requires Confirmation**\n\n` +
			`The command "${command}" has been flagged as potentially risky:\n\n` +
			`**Risk Level:** ${analysis.riskLevel}\n` +
			`**Concerns:** ${analysis.reasons.join(", ")}\n\n` +
			`Do you want to proceed? Consider using the safer alternatives provided.`
	}

	private generateModifiedCommand(command: string, analysis: ProcessAnalysis): string {
		// Apply common modifications to make commands safer
		let modified = command

		// Remove watch flags
		modified = modified.replace(/--watch|-w\b/g, "")
		
		// Add timeout to long-running commands
		if (analysis.category === ProcessCategory.SERVER) {
			modified = `timeout 30s ${modified} || echo "Command timed out after 30 seconds"`
		}
		
		// Add background process prevention
		modified = modified.replace(/\s*&\s*$/, "")
		
		// Add safety wrapper for npm start
		if (/npm\s+start/i.test(command)) {
			modified = `echo "Supervisor: npm start blocked. Use 'npm run build' or 'npm test' instead."`
		}

		return modified.trim()
	}

	private extractFilename(command: string): string {
		// Simple filename extraction - in practice, this would be more sophisticated
		const tokens = command.split(/\s+/)
		for (let i = 1; i < tokens.length; i++) {
			if (!tokens[i].startsWith("-") && tokens[i].includes(".")) {
				return tokens[i]
			}
		}
		return "filename"
	}

	private generateBypassCode(): string {
		const timestamp = Date.now().toString(36)
		const random = Math.random().toString(36).substr(2, 4)
		return `BYPASS_${timestamp}_${random}`.toUpperCase()
	}

	private isValidBypassCode(code: string): boolean {
		// Check if it's a valid bypass code format and not already used
		const isValidFormat = /^BYPASS_[A-Z0-9]+_[A-Z0-9]+$/.test(code)
		const notAlreadyUsed = !this.sessionBypassCodes.has(code)
		const withinLimit = this.bypassCount < this.config.maxBypassesPerSession
		
		return isValidFormat && notAlreadyUsed && withinLimit
	}

	private logInterception(result: InterceptionResult): void {
		const logLevel = result.riskLevel === "critical" ? "error" : 
						result.riskLevel === "high" ? "warn" : "info"
		
		const message = `Interception: ${result.action} - ${result.originalCommand} (${result.riskLevel} risk)`
		this.log(message, logLevel)
		
		if (result.action === "block") {
			this.log(`Blocked reason: ${result.reason}`, "warn")
		}
		
		if (result.safeAlternatives.length > 0) {
			this.log(`Safe alternatives provided: ${result.safeAlternatives.length}`, "info")
		}
	}

	private log(message: string, level: "debug" | "info" | "warn" | "error" = "info"): void {
		const timestamp = new Date().toISOString()
		const logMessage = `[${timestamp}] [InterceptionEngine] ${message}`
		
		this.outputChannel.appendLine(logMessage)
		
		switch (level) {
			case "error":
				console.error(logMessage)
				break
			case "warn":
				console.warn(logMessage)
				break
			case "debug":
				console.debug(logMessage)
				break
			default:
				console.log(logMessage)
		}
	}

	/**
	 * Update configuration
	 */
	public updateConfig(newConfig: Partial<InterceptionConfig>): void {
		this.config = { ...this.config, ...newConfig }
		this.log("Interception configuration updated", "info")
	}

	/**
	 * Get current statistics
	 */
	public getStats(): {
		bypassCount: number
		maxBypasses: number
		strictMode: boolean
		allowUserOverrides: boolean
	} {
		return {
			bypassCount: this.bypassCount,
			maxBypasses: this.config.maxBypassesPerSession,
			strictMode: this.config.strictMode,
			allowUserOverrides: this.config.allowUserOverrides
		}
	}

	/**
	 * Reset session state
	 */
	public resetSession(): void {
		this.bypassCount = 0
		this.sessionBypassCodes.clear()
		this.log("Interception session reset", "info")
	}
}
