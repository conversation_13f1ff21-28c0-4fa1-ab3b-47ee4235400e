import * as vscode from "vscode"
import * as fs from "fs/promises"
import * as path from "path"
import type { SupervisorDecision } from "./AutonomousSupervisor"

export interface LogEntry {
	timestamp: number
	level: "debug" | "info" | "warn" | "error"
	category: "decision" | "process" | "config" | "performance" | "security"
	message: string
	data?: any
	sessionId: string
}

export interface PerformanceMetrics {
	sessionStartTime: number
	totalDecisions: number
	blockedCommands: number
	modifiedCommands: number
	allowedCommands: number
	averageDecisionTime: number
	processesMonitored: number
	processesTerminated: number
	emergencyKills: number
	sessionUptime: number
}

export interface SecurityMetrics {
	highRiskCommandsBlocked: number
	criticalRiskCommandsBlocked: number
	bypassCodesUsed: number
	suspiciousPatterns: string[]
	topBlockedCommands: Array<{ command: string; count: number }>
}

/**
 * Comprehensive Logging and Monitoring System for the Autonomous Supervisor
 * 
 * Provides structured logging, performance metrics, security monitoring,
 * and session responsiveness tracking.
 */
export class SupervisorLogger {
	private logEntries: LogEntry[] = []
	private performanceMetrics: PerformanceMetrics
	private securityMetrics: SecurityMetrics
	private outputChannel: vscode.OutputChannel
	private sessionId: string
	private logFilePath?: string
	private decisionTimes: number[] = []
	private commandCounts = new Map<string, number>()

	constructor(outputChannel: vscode.OutputChannel, sessionId: string) {
		this.outputChannel = outputChannel
		this.sessionId = sessionId
		
		this.performanceMetrics = {
			sessionStartTime: Date.now(),
			totalDecisions: 0,
			blockedCommands: 0,
			modifiedCommands: 0,
			allowedCommands: 0,
			averageDecisionTime: 0,
			processesMonitored: 0,
			processesTerminated: 0,
			emergencyKills: 0,
			sessionUptime: 0
		}
		
		this.securityMetrics = {
			highRiskCommandsBlocked: 0,
			criticalRiskCommandsBlocked: 0,
			bypassCodesUsed: 0,
			suspiciousPatterns: [],
			topBlockedCommands: []
		}
		
		this.initializeLogFile()
	}

	/**
	 * Log a supervisor decision
	 */
	public logDecision(decision: SupervisorDecision, decisionTimeMs: number): void {
		this.performanceMetrics.totalDecisions++
		this.decisionTimes.push(decisionTimeMs)
		this.performanceMetrics.averageDecisionTime = 
			this.decisionTimes.reduce((a, b) => a + b, 0) / this.decisionTimes.length

		// Update decision counters
		switch (decision.action) {
			case "block":
				this.performanceMetrics.blockedCommands++
				if (decision.originalCommand) {
					this.incrementCommandCount(decision.originalCommand)
				}
				break
			case "modify":
				this.performanceMetrics.modifiedCommands++
				break
			case "allow":
				this.performanceMetrics.allowedCommands++
				break
		}

		// Update security metrics
		if (decision.riskLevel === "high") {
			this.securityMetrics.highRiskCommandsBlocked++
		} else if (decision.riskLevel === "critical") {
			this.securityMetrics.criticalRiskCommandsBlocked++
		}

		// Log the decision
		this.log("info", "decision", `Decision: ${decision.action} (${decision.riskLevel} risk) - ${decision.reason}`, {
			decision,
			decisionTimeMs
		})
	}

	/**
	 * Log process monitoring events
	 */
	public logProcessEvent(event: "registered" | "unregistered" | "timeout" | "killed", pid: number, command?: string): void {
		switch (event) {
			case "registered":
				this.performanceMetrics.processesMonitored++
				this.log("debug", "process", `Process registered: ${pid} - ${command}`, { pid, command })
				break
			case "unregistered":
				this.log("debug", "process", `Process unregistered: ${pid}`, { pid })
				break
			case "timeout":
				this.performanceMetrics.processesTerminated++
				this.log("warn", "process", `Process timeout: ${pid} - ${command}`, { pid, command })
				break
			case "killed":
				this.performanceMetrics.emergencyKills++
				this.log("error", "process", `Emergency kill: ${pid} - ${command}`, { pid, command })
				break
		}
	}

	/**
	 * Log configuration changes
	 */
	public logConfigChange(changes: Record<string, any>): void {
		this.log("info", "config", "Configuration updated", changes)
	}

	/**
	 * Log security events
	 */
	public logSecurityEvent(event: "bypass_used" | "suspicious_pattern" | "repeated_attempts", data: any): void {
		switch (event) {
			case "bypass_used":
				this.securityMetrics.bypassCodesUsed++
				this.log("warn", "security", "Bypass code used", data)
				break
			case "suspicious_pattern":
				if (!this.securityMetrics.suspiciousPatterns.includes(data.pattern)) {
					this.securityMetrics.suspiciousPatterns.push(data.pattern)
				}
				this.log("warn", "security", `Suspicious pattern detected: ${data.pattern}`, data)
				break
			case "repeated_attempts":
				this.log("error", "security", "Repeated blocked command attempts", data)
				break
		}
	}

	/**
	 * Log performance metrics
	 */
	public logPerformanceMetrics(): void {
		this.performanceMetrics.sessionUptime = Date.now() - this.performanceMetrics.sessionStartTime
		
		this.log("info", "performance", "Performance metrics update", {
			...this.performanceMetrics,
			topBlockedCommands: this.getTopBlockedCommands(5)
		})
	}

	/**
	 * Generic logging method
	 */
	public log(level: "debug" | "info" | "warn" | "error", category: "decision" | "process" | "config" | "performance" | "security", message: string, data?: any): void {
		const entry: LogEntry = {
			timestamp: Date.now(),
			level,
			category,
			message,
			data,
			sessionId: this.sessionId
		}

		this.logEntries.push(entry)
		
		// Keep only last 10000 entries to prevent memory leaks
		if (this.logEntries.length > 10000) {
			this.logEntries = this.logEntries.slice(-10000)
		}

		// Output to VSCode channel
		const timestamp = new Date(entry.timestamp).toISOString()
		const logMessage = `[${timestamp}] [${category.toUpperCase()}] [${level.toUpperCase()}] ${message}`
		
		this.outputChannel.appendLine(logMessage)
		
		if (data) {
			this.outputChannel.appendLine(`  Data: ${JSON.stringify(data, null, 2)}`)
		}

		// Console output based on level
		switch (level) {
			case "error":
				console.error(logMessage, data)
				break
			case "warn":
				console.warn(logMessage, data)
				break
			case "debug":
				console.debug(logMessage, data)
				break
			default:
				console.log(logMessage, data)
		}

		// Write to log file if available
		this.writeToLogFile(entry)
	}

	/**
	 * Get current performance metrics
	 */
	public getPerformanceMetrics(): PerformanceMetrics {
		this.performanceMetrics.sessionUptime = Date.now() - this.performanceMetrics.sessionStartTime
		return { ...this.performanceMetrics }
	}

	/**
	 * Get current security metrics
	 */
	public getSecurityMetrics(): SecurityMetrics {
		return {
			...this.securityMetrics,
			topBlockedCommands: this.getTopBlockedCommands(10)
		}
	}

	/**
	 * Get recent log entries
	 */
	public getRecentLogs(limit: number = 100, level?: "debug" | "info" | "warn" | "error", category?: string): LogEntry[] {
		let filtered = this.logEntries

		if (level) {
			filtered = filtered.filter(entry => entry.level === level)
		}

		if (category) {
			filtered = filtered.filter(entry => entry.category === category)
		}

		return filtered.slice(-limit)
	}

	/**
	 * Export logs to JSON
	 */
	public exportLogs(): string {
		return JSON.stringify({
			sessionId: this.sessionId,
			exportTime: Date.now(),
			performanceMetrics: this.getPerformanceMetrics(),
			securityMetrics: this.getSecurityMetrics(),
			logEntries: this.logEntries
		}, null, 2)
	}

	/**
	 * Generate summary report
	 */
	public generateSummaryReport(): string {
		const perf = this.getPerformanceMetrics()
		const security = this.getSecurityMetrics()
		const uptime = Math.round(perf.sessionUptime / 1000 / 60) // minutes
		
		return `
# Supervisor Session Summary

**Session ID:** ${this.sessionId}
**Uptime:** ${uptime} minutes
**Total Decisions:** ${perf.totalDecisions}

## Command Statistics
- **Allowed:** ${perf.allowedCommands}
- **Blocked:** ${perf.blockedCommands}
- **Modified:** ${perf.modifiedCommands}
- **Average Decision Time:** ${perf.averageDecisionTime.toFixed(2)}ms

## Process Monitoring
- **Processes Monitored:** ${perf.processesMonitored}
- **Processes Terminated:** ${perf.processesTerminated}
- **Emergency Kills:** ${perf.emergencyKills}

## Security Events
- **High Risk Commands Blocked:** ${security.highRiskCommandsBlocked}
- **Critical Risk Commands Blocked:** ${security.criticalRiskCommandsBlocked}
- **Bypass Codes Used:** ${security.bypassCodesUsed}
- **Suspicious Patterns:** ${security.suspiciousPatterns.length}

## Top Blocked Commands
${security.topBlockedCommands.map((cmd, i) => `${i + 1}. ${cmd.command} (${cmd.count} times)`).join('\n')}
		`.trim()
	}

	private async initializeLogFile(): Promise<void> {
		try {
			const workspaceFolder = vscode.workspace.workspaceFolders?.[0]
			if (workspaceFolder) {
				const logsDir = path.join(workspaceFolder.uri.fsPath, ".vscode", "supervisor-logs")
				await fs.mkdir(logsDir, { recursive: true })
				
				const timestamp = new Date().toISOString().replace(/[:.]/g, "-")
				this.logFilePath = path.join(logsDir, `supervisor-${timestamp}.log`)
			}
		} catch (error) {
			console.warn("Failed to initialize supervisor log file:", error)
		}
	}

	private async writeToLogFile(entry: LogEntry): Promise<void> {
		if (!this.logFilePath) return

		try {
			const logLine = JSON.stringify(entry) + '\n'
			await fs.appendFile(this.logFilePath, logLine)
		} catch (error) {
			// Silently fail to avoid logging loops
		}
	}

	private incrementCommandCount(command: string): void {
		const normalizedCommand = command.toLowerCase().trim()
		this.commandCounts.set(normalizedCommand, (this.commandCounts.get(normalizedCommand) || 0) + 1)
	}

	private getTopBlockedCommands(limit: number): Array<{ command: string; count: number }> {
		return Array.from(this.commandCounts.entries())
			.map(([command, count]) => ({ command, count }))
			.sort((a, b) => b.count - a.count)
			.slice(0, limit)
	}

	/**
	 * Cleanup resources
	 */
	public dispose(): void {
		this.logEntries = []
		this.decisionTimes = []
		this.commandCounts.clear()
	}
}
