# Supervisor Integration Guide

This guide explains how the Autonomous Supervisor is integrated into the Kilo Code extension.

## Integration Points

### 1. Extension Activation (`src/extension.ts`)

The supervisor is initialized during extension activation:

```typescript
import { AutonomousSupervisor } from "./services/supervisor/AutonomousSupervisor"

// In activate() function
try {
  const supervisor = AutonomousSupervisor.getInstance(outputChannel)
  outputChannel.appendLine("Autonomous Supervisor initialized successfully")
  
  // Add supervisor cleanup to extension disposal
  context.subscriptions.push({
    dispose: () => {
      supervisor.dispose()
    }
  })
} catch (error) {
  outputChannel.appendLine(`Failed to initialize Autonomous Supervisor: ${error}`)
  console.warn("Supervisor initialization failed:", error)
}
```

### 2. Command Execution (`src/core/tools/executeCommandTool.ts`)

Commands are monitored before execution:

```typescript
import { AutonomousSupervisor } from "../../services/supervisor/AutonomousSupervisor"
import { InterceptionEngine } from "../../services/supervisor/InterceptionEngine"

// Before command approval
const provider = await task.providerRef.deref()
const outputChannel = provider?.outputChannel
if (outputChannel) {
  try {
    const supervisor = AutonomousSupervisor.getInstance(outputChannel)
    const interceptionEngine = new InterceptionEngine(outputChannel)
    
    // Monitor the command through supervisor
    const supervisorResult = await supervisor.monitorCommand(command, task)
    if (!supervisorResult.allowed) {
      // Command was blocked by supervisor
      pushToolResult(formatResponse.toolError(
        `🚫 **Command Blocked by Supervisor**\n\n` +
        `**Command:** \`${command}\`\n` +
        `**Reason:** ${supervisorResult.reason}\n\n` +
        `The supervisor prevents potentially unbounded processes...`
      ))
      return
    }
    
    // Check for detailed interception analysis
    const interceptionResult = await interceptionEngine.interceptCommand(command, task.cwd)
    if (interceptionResult.action === "block") {
      // Show detailed refusal message with alternatives
      let refusalMessage = interceptionResult.reason
      
      if (interceptionResult.safeAlternatives.length > 0) {
        refusalMessage += `\n\n**💡 Safe Alternatives:**\n\`\`\`bash\n${interceptionResult.safeAlternatives.join('\n')}\n\`\`\``
      }
      
      pushToolResult(formatResponse.toolError(refusalMessage))
      return
    }
  } catch (supervisorError) {
    // Log supervisor error but don't block execution
    console.warn("Supervisor error:", supervisorError)
    outputChannel.appendLine(`[Supervisor] Error: ${supervisorError}`)
  }
}
```

### 3. Process Registration

Processes are registered for monitoring when they start:

```typescript
onShellExecutionStarted: (pid: number | undefined) => {
  console.log(`[executeCommand] onShellExecutionStarted: ${pid}`)
  const status: CommandExecutionStatus = { executionId, status: "started", pid, command }
  provider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })
  
  // Register process with supervisor for monitoring
  if (pid && provider?.outputChannel) {
    try {
      const supervisor = AutonomousSupervisor.getInstance(provider.outputChannel)
      supervisor.registerProcess(pid, command)
    } catch (error) {
      console.warn("Failed to register process with supervisor:", error)
    }
  }
}
```

And unregistered when they complete:

```typescript
onShellExecutionComplete: (details: ExitCodeDetails) => {
  const status: CommandExecutionStatus = { executionId, status: "exited", exitCode: details.exitCode }
  provider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })
  exitDetails = details
  
  // Unregister process from supervisor when it completes
  if (status.pid && provider?.outputChannel) {
    try {
      const supervisor = AutonomousSupervisor.getInstance(provider.outputChannel)
      supervisor.unregisterProcess(status.pid)
    } catch (error) {
      console.warn("Failed to unregister process from supervisor:", error)
    }
  }
}
```

### 4. Message Monitoring (`src/core/webview/webviewMessageHandler.ts`)

Webview messages are monitored for risky task requests:

```typescript
import { AutonomousSupervisor } from "../../services/supervisor/AutonomousSupervisor"

case "newTask":
  // Supervisor integration - monitor new task messages
  try {
    const supervisor = AutonomousSupervisor.getInstance(provider.outputChannel)
    const monitoredMessage = await supervisor.monitorMessage(message)
    
    if (!monitoredMessage) {
      // Message was blocked by supervisor - just break without creating task
      break
    }
    
    // Use the potentially modified message
    const taskText = monitoredMessage.text || message.text
    const taskImages = monitoredMessage.images || message.images
    
    // Initializing new instance of Cline...
    await provider.initClineWithTask(taskText, taskImages)
  } catch (supervisorError) {
    // Log supervisor error but don't block task creation
    console.warn("Supervisor error in newTask:", supervisorError)
    provider.outputChannel.appendLine(`[Supervisor] Error in newTask: ${supervisorError}`)
    
    // Fall back to original behavior
    await provider.initClineWithTask(message.text, message.images)
  }
  break
```

### 5. Configuration (`src/package.json`)

VSCode settings are defined in package.json:

```json
{
  "configuration": {
    "properties": {
      "kilo-code.supervisor.enabled": {
        "type": "boolean",
        "default": true,
        "description": "Enable the Autonomous Supervisor to monitor and control potentially unbounded processes"
      },
      "kilo-code.supervisor.maxProcessTimeout": {
        "type": "number",
        "default": 30000,
        "minimum": 5000,
        "maximum": 300000,
        "description": "Maximum time (in milliseconds) a process can run before being terminated by the supervisor"
      },
      "kilo-code.supervisor.unboundedProcessPatterns": {
        "type": "array",
        "items": { "type": "string" },
        "default": [
          "npm start",
          "yarn start",
          "pnpm start",
          "npm run dev",
          "yarn dev",
          "pnpm dev",
          "nodemon",
          "pm2 start",
          "webpack-dev-server",
          "vite",
          "next dev",
          "tail -f",
          "watch ",
          "while true",
          "docker-compose up"
        ],
        "description": "Patterns that match potentially unbounded processes that should be blocked or modified"
      }
    }
  }
}
```

## Error Handling Strategy

The supervisor is designed to fail gracefully:

1. **Non-blocking errors** - Supervisor errors don't prevent command execution
2. **Fallback behavior** - Falls back to original behavior on supervisor failure
3. **Comprehensive logging** - All errors are logged for debugging
4. **Graceful degradation** - System continues working even if supervisor fails

## Testing Integration

The supervisor integration is tested through:

1. **Unit tests** - Individual component testing
2. **Integration tests** - End-to-end command flow testing
3. **Performance tests** - Impact on command execution timing
4. **Error handling tests** - Graceful failure scenarios

## Monitoring and Debugging

### Output Channel Logging

All supervisor activities are logged to the "Kilo Code" output channel:

```
[2024-01-15T10:30:45.123Z] [Supervisor] Autonomous Supervisor initialized
[2024-01-15T10:30:46.456Z] [Supervisor] Decision: block - Command matches unbounded process pattern: npm start
[2024-01-15T10:30:47.789Z] [Supervisor] Process registered: 12345 - node server.js
```

### Performance Metrics

Access performance metrics programmatically:

```typescript
const supervisor = AutonomousSupervisor.getInstance(outputChannel)
const metrics = supervisor.getPerformanceMetrics()
console.log(`Average decision time: ${metrics.averageDecisionTime}ms`)
```

### Debug Commands

Add debug commands for development:

```typescript
// In command palette
vscode.commands.registerCommand('kilo-code.supervisor.stats', () => {
  const supervisor = AutonomousSupervisor.getInstance(outputChannel)
  const report = supervisor.generateSummaryReport()
  vscode.window.showInformationMessage(report)
})
```

## Configuration Management

### Runtime Configuration Updates

```typescript
const supervisor = AutonomousSupervisor.getInstance(outputChannel)

// Update configuration
await supervisor.updateConfig({
  maxProcessTimeout: 60000,
  blockNpmStart: false
})

// Listen for configuration changes
supervisor.configManager.onConfigurationChange((newConfig) => {
  console.log('Configuration updated:', newConfig)
})
```

### User Settings

Users can customize supervisor behavior through VSCode settings:

1. Open VSCode Settings (Ctrl+,)
2. Search for "kilo-code.supervisor"
3. Modify settings as needed
4. Changes take effect immediately

## Deployment Considerations

### Production Deployment

- Supervisor is enabled by default
- Conservative settings for maximum safety
- Comprehensive logging for monitoring
- Performance optimized for minimal impact

### Development Environment

- More permissive settings for development
- Detailed logging for debugging
- Easy configuration overrides
- Emergency bypass codes available

### Enterprise Deployment

- Centralized configuration management
- Audit logging for compliance
- Custom pattern definitions
- Integration with security monitoring
