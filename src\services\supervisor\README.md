# Autonomous Supervisor System

The Autonomous Supervisor is a comprehensive monitoring and control system that continuously monitors every message, model invocation, and terminal command across the entire session. It automatically intercepts and blocks potentially unbounded processes to maintain session responsiveness.

## Architecture Overview

```mermaid
graph TB
    A[User Input] --> B[WebView Message Handler]
    B --> C[Autonomous Supervisor]
    C --> D[Process Detector]
    C --> E[Interception Engine]
    C --> F[Configuration Manager]
    C --> G[Logger]
    
    D --> H[Pattern Analysis]
    D --> I[Semantic Analysis]
    D --> J[Contextual Analysis]
    D --> K[Structural Analysis]
    
    E --> L[Block Command]
    E --> M[Modify Command]
    E --> N[Allow Command]
    E --> O[Generate Alternatives]
    
    C --> P[Execute Command Tool]
    P --> Q[Terminal Process]
    Q --> R[Process Registration]
    R --> C
```

## Core Components

### 1. AutonomousSupervisor
The main orchestrator that coordinates all monitoring activities.

**Key Features:**
- Singleton pattern for global access
- Event-driven architecture
- Real-time process monitoring
- Configuration-driven behavior
- Comprehensive logging and metrics

**Methods:**
- `monitorMessage(message)` - Monitor webview messages
- `monitorCommand(command)` - Monitor command execution
- `registerProcess(pid, command)` - Register process for monitoring
- `unregisterProcess(pid)` - Unregister completed process

### 2. ProcessDetector
Advanced detection engine using multiple analysis strategies.

**Detection Strategies:**
1. **Pattern Matching** - Regex-based detection of known unbounded patterns
2. **Semantic Analysis** - Keyword-based risk assessment
3. **Contextual Analysis** - Working directory and environment context
4. **Structural Analysis** - Command complexity and structure analysis

**Risk Categories:**
- `SAFE` - Normal system commands
- `BUILD_TOOL` - Build and compilation commands
- `TEST_RUNNER` - Testing frameworks
- `SERVER` - Development and production servers
- `DATABASE` - Database servers and services
- `WATCHER` - File watchers and monitors
- `DAEMON` - Background processes and daemons
- `INTERACTIVE` - Interactive applications
- `NETWORK` - Network services and listeners
- `SYSTEM` - System-level operations

### 3. InterceptionEngine
Intelligent command interception with safe alternatives.

**Actions:**
- `allow` - Command is safe to execute
- `block` - Command is blocked with explanation
- `modify` - Command is modified for safety
- `prompt` - User confirmation required

**Features:**
- Safe alternative generation
- Emergency bypass codes
- User override mechanisms
- Contextual refusal messages

### 4. SupervisorConfigManager
Configuration management with VSCode settings integration.

**Configuration Options:**
```typescript
interface SupervisorConfig {
  enabled: boolean
  maxProcessTimeout: number
  unboundedProcessPatterns: string[]
  allowedLongRunningCommands: string[]
  blockServerCommands: boolean
  blockNpmStart: boolean
  logDecisions: boolean
  emergencyKillTimeout: number
}
```

### 5. SupervisorLogger
Comprehensive logging and monitoring system.

**Metrics Tracked:**
- Performance metrics (decision times, process counts)
- Security metrics (blocked commands, risk levels)
- Session responsiveness
- Command statistics
- Process lifecycle events

## Configuration

### VSCode Settings

Add these settings to your VSCode configuration:

```json
{
  "kilo-code.supervisor.enabled": true,
  "kilo-code.supervisor.maxProcessTimeout": 30000,
  "kilo-code.supervisor.blockServerCommands": true,
  "kilo-code.supervisor.blockNpmStart": true,
  "kilo-code.supervisor.logDecisions": true,
  "kilo-code.supervisor.unboundedProcessPatterns": [
    "npm start",
    "yarn dev",
    "webpack-dev-server",
    "tail -f",
    "while true"
  ],
  "kilo-code.supervisor.allowedLongRunningCommands": []
}
```

### Runtime Configuration

```typescript
const supervisor = AutonomousSupervisor.getInstance(outputChannel)

// Update configuration
await supervisor.updateConfig({
  maxProcessTimeout: 60000,
  blockNpmStart: false
})

// Get current configuration
const config = supervisor.configManager.loadConfiguration()
```

## Usage Examples

### Basic Command Monitoring

```typescript
const supervisor = AutonomousSupervisor.getInstance(outputChannel)

// Monitor a command
const result = await supervisor.monitorCommand("npm start")
if (!result.allowed) {
  console.log(`Command blocked: ${result.reason}`)
}
```

### Process Registration

```typescript
// Register process when it starts
supervisor.registerProcess(pid, command)

// Unregister when it completes
supervisor.unregisterProcess(pid)
```

### Event Handling

```typescript
supervisor.on('processBlocked', (command, reason) => {
  console.log(`Blocked: ${command} - ${reason}`)
})

supervisor.on('processModified', (original, modified, reason) => {
  console.log(`Modified: ${original} -> ${modified}`)
})

supervisor.on('emergencyKill', (pid, reason) => {
  console.log(`Emergency kill: ${pid} - ${reason}`)
})
```

### Metrics and Reporting

```typescript
// Get performance metrics
const metrics = supervisor.getPerformanceMetrics()
console.log(`Total decisions: ${metrics.totalDecisions}`)

// Generate summary report
const report = supervisor.generateSummaryReport()
console.log(report)

// Export logs
const logs = supervisor.exportLogs()
fs.writeFileSync('supervisor-logs.json', logs)
```

## Blocked Command Patterns

### Server Commands
- `npm start`, `yarn start`, `pnpm start`
- `npm run dev`, `yarn dev`, `pnpm dev`
- `webpack-dev-server`, `vite`, `next dev`
- `python -m http.server`
- `nodemon`, `pm2 start`

### Database Services
- `mongod`, `redis-server`
- `postgres`, `mysql`
- `elasticsearch`

### File Watchers
- `tail -f filename`
- `watch command`
- `webpack --watch`
- `tsc --watch`

### Long-Running Processes
- `while true; do ...; done`
- `for (;;) { ... }`
- `sleep 3600` (long sleeps)
- `docker-compose up`

### Interactive Applications
- `vim`, `nano`, `emacs`
- `ssh`, `telnet`
- `nc -l`, `netcat -l`

## Safe Alternatives

The supervisor automatically suggests safe alternatives:

| Blocked Command | Safe Alternative |
|----------------|------------------|
| `npm start` | `npm run build && npm test` |
| `tail -f log.txt` | `head -n 50 log.txt` |
| `webpack --watch` | `webpack` (single build) |
| `python -m http.server` | `curl http://localhost:8000` |
| `docker-compose up` | `docker-compose build` |

## Emergency Bypass

For critical situations, the supervisor provides emergency bypass codes:

```bash
# Supervisor will provide a bypass code like:
# BYPASS_ABC123_XYZ789

# Use it to override blocking (limited uses per session)
```

## Testing

Run the comprehensive test suite:

```bash
npm test src/services/supervisor
```

Tests cover:
- Command detection accuracy
- Risk level assessment
- Configuration management
- Process monitoring
- Event emission
- Error handling
- Performance metrics

## Troubleshooting

### Common Issues

1. **Commands still blocked after configuration change**
   - Restart VSCode to reload configuration
   - Check configuration syntax in settings.json

2. **False positives blocking safe commands**
   - Add commands to `allowedLongRunningCommands`
   - Adjust `unboundedProcessPatterns`

3. **Performance impact**
   - Disable logging with `logDecisions: false`
   - Increase `maxProcessTimeout` for legitimate long-running tasks

### Debug Information

Enable debug logging:
```typescript
supervisor.logger.log('debug', 'config', 'Debug message', { data })
```

View recent logs:
```typescript
const logs = supervisor.getRecentLogs(100, 'error')
```

## Contributing

When adding new detection patterns:

1. Add pattern to `ProcessDetector.initializeDetectionRules()`
2. Include risk level and confidence score
3. Provide suggested alternative
4. Add comprehensive tests
5. Update documentation

## Security Considerations

The supervisor is designed with security in mind:

- **Fail-safe defaults** - Block by default when uncertain
- **Limited bypass codes** - Maximum 3 per session
- **Comprehensive logging** - All decisions are logged
- **Process isolation** - Monitored processes are properly terminated
- **Configuration validation** - All settings are validated

## Performance Impact

The supervisor is optimized for minimal performance impact:

- **Lazy evaluation** - Only analyzes when necessary
- **Efficient pattern matching** - Compiled regex patterns
- **Memory management** - Limited log retention
- **Asynchronous operations** - Non-blocking analysis
- **Configurable timeouts** - Adjustable based on needs
