import { describe, it, expect, beforeEach } from "vitest"
import { ProcessDetector, ProcessCategory } from "../ProcessDetector"

describe("ProcessDetector", () => {
	let detector: ProcessDetector

	beforeEach(() => {
		detector = new ProcessDetector()
	})

	describe("Server Command Detection", () => {
		it("should detect npm start as unbounded server command", () => {
			const analysis = detector.analyzeCommand("npm start")
			
			expect(analysis.isUnbounded).toBe(true)
			expect(analysis.category).toBe(ProcessCategory.SERVER)
			expect(analysis.riskLevel).toBe("critical")
			expect(analysis.confidence).toBeGreaterThan(0.8)
			expect(analysis.suggestedAlternative).toContain("build")
		})

		it("should detect various development servers", () => {
			const serverCommands = [
				"yarn dev",
				"pnpm start",
				"webpack-dev-server",
				"vite",
				"next dev",
				"nuxt dev",
				"gatsby develop",
				"hugo server",
				"jekyll serve"
			]

			for (const command of serverCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(true)
				expect([ProcessCategory.SERVER].includes(analysis.category)).toBe(true)
				expect(analysis.riskLevel).toMatch(/high|critical/)
			}
		})

		it("should detect Python HTTP server", () => {
			const analysis = detector.analyzeCommand("python -m http.server 8000")
			
			expect(analysis.isUnbounded).toBe(true)
			expect(analysis.category).toBe(ProcessCategory.SERVER)
			expect(analysis.riskLevel).toBe("critical")
		})
	})

	describe("Database and Service Detection", () => {
		it("should detect database servers", () => {
			const dbCommands = [
				"mongod",
				"redis-server",
				"postgres",
				"mysql",
				"elasticsearch"
			]

			for (const command of dbCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(true)
				expect(analysis.category).toBe(ProcessCategory.DATABASE)
				expect(analysis.riskLevel).toBe("high")
			}
		})

		it("should detect Docker Compose", () => {
			const analysis = detector.analyzeCommand("docker-compose up")
			
			expect(analysis.isUnbounded).toBe(true)
			expect(analysis.category).toBe(ProcessCategory.SYSTEM)
			expect(analysis.riskLevel).toBe("high")
		})
	})

	describe("Watcher and Monitor Detection", () => {
		it("should detect file watchers", () => {
			const watcherCommands = [
				"tail -f logfile.txt",
				"watch ls",
				"webpack --watch",
				"tsc --watch",
				"sass --watch"
			]

			for (const command of watcherCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(true)
				expect(analysis.category).toBe(ProcessCategory.WATCHER)
				expect(analysis.riskLevel).toMatch(/medium|high/)
			}
		})
	})

	describe("Interactive Process Detection", () => {
		it("should detect interactive editors", () => {
			const editorCommands = [
				"vim file.txt",
				"nano file.txt",
				"emacs file.txt",
				"code file.txt"
			]

			for (const command of editorCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(true)
				expect(analysis.category).toBe(ProcessCategory.INTERACTIVE)
				expect(analysis.riskLevel).toBe("medium")
			}
		})
	})

	describe("Network Process Detection", () => {
		it("should detect network listeners", () => {
			const networkCommands = [
				"nc -l 8080",
				"netcat -l 9000",
				"ssh user@host",
				"telnet host"
			]

			for (const command of networkCommands) {
				const analysis = detector.analyzeCommand(command)
				if (command.includes("-l")) {
					expect(analysis.isUnbounded).toBe(true)
					expect(analysis.category).toBe(ProcessCategory.NETWORK)
					expect(analysis.riskLevel).toBe("high")
				}
			}
		})
	})

	describe("Infinite Loop Detection", () => {
		it("should detect infinite loops", () => {
			const loopCommands = [
				"while true; do echo hello; done",
				"while 1; do sleep 1; done",
				"while :; do date; done",
				"for (;;) { echo test; }"
			]

			for (const command of loopCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(true)
				expect(analysis.category).toBe(ProcessCategory.SYSTEM)
				expect(analysis.riskLevel).toBe("critical")
			}
		})

		it("should detect long sleep commands", () => {
			const sleepCommands = [
				"sleep 3600",
				"sleep 1h",
				"sleep 30m",
				"sleep 1d"
			]

			for (const command of sleepCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(true)
				expect(analysis.riskLevel).toBe("medium")
			}
		})
	})

	describe("Safe Command Detection", () => {
		it("should detect safe system commands", () => {
			const safeCommands = [
				"ls -la",
				"cat file.txt",
				"echo hello",
				"pwd",
				"whoami",
				"date",
				"which node"
			]

			for (const command of safeCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(false)
				expect(analysis.category).toBe(ProcessCategory.SAFE)
				expect(analysis.riskLevel).toBe("low")
			}
		})

		it("should detect safe version control commands", () => {
			const gitCommands = [
				"git status",
				"git log",
				"git diff",
				"git show",
				"svn status",
				"hg log"
			]

			for (const command of gitCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(false)
				expect(analysis.category).toBe(ProcessCategory.SAFE)
				expect(analysis.riskLevel).toBe("low")
			}
		})

		it("should detect safe package manager commands", () => {
			const packageCommands = [
				"npm install",
				"npm test",
				"npm run build",
				"yarn install",
				"yarn test",
				"pnpm install"
			]

			for (const command of packageCommands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.isUnbounded).toBe(false)
				expect(analysis.category).toBe(ProcessCategory.BUILD_TOOL)
				expect(analysis.riskLevel).toBe("low")
			}
		})
	})

	describe("Semantic Analysis", () => {
		it("should increase risk for server-related keywords", () => {
			const serverCommand = "start the development server"
			const analysis = detector.analyzeCommand(serverCommand)
			
			expect(analysis.riskLevel).toMatch(/medium|high|critical/)
			expect(analysis.reasons.some(r => r.includes("Semantic"))).toBe(true)
		})

		it("should decrease risk for build-related keywords", () => {
			const buildCommand = "build the project for production"
			const analysis = detector.analyzeCommand(buildCommand)
			
			expect(analysis.riskLevel).toBe("low")
		})
	})

	describe("Contextual Analysis", () => {
		it("should consider working directory context", () => {
			const command = "npm start"
			const workingDir = "/path/to/project"
			
			const analysis = detector.analyzeCommand(command, workingDir)
			
			expect(analysis.isUnbounded).toBe(true)
			expect(analysis.reasons.some(r => r.includes("npm start"))).toBe(true)
		})

		it("should detect port numbers", () => {
			const command = "node server.js --port 3000"
			const analysis = detector.analyzeCommand(command)
			
			expect(analysis.reasons.some(r => r.includes("port"))).toBe(true)
		})

		it("should detect background process indicators", () => {
			const commands = [
				"node server.js &",
				"nohup python script.py"
			]

			for (const command of commands) {
				const analysis = detector.analyzeCommand(command)
				expect(analysis.reasons.some(r => r.includes("background"))).toBe(true)
			}
		})
	})

	describe("Structural Analysis", () => {
		it("should analyze command complexity", () => {
			const complexCommand = "cat file1.txt | grep pattern | sort | uniq | head -10"
			const analysis = detector.analyzeCommand(complexCommand)
			
			expect(analysis.reasons.some(r => r.includes("pipe"))).toBe(true)
		})

		it("should detect multiple chained commands", () => {
			const chainedCommand = "npm install && npm run build && npm test && npm start"
			const analysis = detector.analyzeCommand(chainedCommand)
			
			expect(analysis.reasons.some(r => r.includes("chained"))).toBe(true)
		})

		it("should detect shell scripting constructs", () => {
			const scriptCommand = "if [ -f package.json ]; then npm install; fi"
			const analysis = detector.analyzeCommand(scriptCommand)
			
			expect(analysis.reasons.some(r => r.includes("scripting"))).toBe(true)
		})
	})

	describe("Combined Analysis", () => {
		it("should combine multiple analysis methods", () => {
			const command = "npm start --port 3000 && tail -f logs/server.log"
			const analysis = detector.analyzeCommand(command)
			
			expect(analysis.isUnbounded).toBe(true)
			expect(analysis.riskLevel).toBe("critical")
			expect(analysis.confidence).toBeGreaterThan(0.7)
			expect(analysis.reasons.length).toBeGreaterThan(1)
		})

		it("should provide appropriate confidence levels", () => {
			const highConfidenceCommand = "npm start"
			const lowConfidenceCommand = "some-unknown-command"
			
			const highAnalysis = detector.analyzeCommand(highConfidenceCommand)
			const lowAnalysis = detector.analyzeCommand(lowConfidenceCommand)
			
			expect(highAnalysis.confidence).toBeGreaterThan(lowAnalysis.confidence)
		})
	})

	describe("Suggested Alternatives", () => {
		it("should provide alternatives for server commands", () => {
			const analysis = detector.analyzeCommand("npm start")
			
			expect(analysis.suggestedAlternative).toBeDefined()
			expect(analysis.suggestedAlternative).toContain("build")
		})

		it("should provide alternatives for watcher commands", () => {
			const analysis = detector.analyzeCommand("webpack --watch")
			
			expect(analysis.suggestedAlternative).toBeDefined()
		})

		it("should provide alternatives for database commands", () => {
			const analysis = detector.analyzeCommand("mongod")
			
			expect(analysis.suggestedAlternative).toBeDefined()
		})
	})

	describe("Edge Cases", () => {
		it("should handle empty commands", () => {
			const analysis = detector.analyzeCommand("")
			
			expect(analysis.isUnbounded).toBe(false)
			expect(analysis.riskLevel).toBe("low")
		})

		it("should handle very long commands", () => {
			const longCommand = "a".repeat(1000)
			const analysis = detector.analyzeCommand(longCommand)
			
			expect(analysis).toBeDefined()
			expect(analysis.reasons.some(r => r.includes("long"))).toBe(true)
		})

		it("should handle commands with special characters", () => {
			const specialCommand = "echo 'Hello $USER! Today is $(date)'"
			const analysis = detector.analyzeCommand(specialCommand)
			
			expect(analysis).toBeDefined()
		})
	})
})
