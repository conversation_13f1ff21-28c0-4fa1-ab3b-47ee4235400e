import { EventEmitter } from "events"
import * as vscode from "vscode"

import type { WebviewMessage } from "../../shared/WebviewMessage"
import type { Task } from "../../core/task/Task"
import { Package } from "../../shared/package"
import { SupervisorConfigManager } from "./SupervisorConfigManager"
import { SupervisorLogger } from "./SupervisorLogger"

export interface SupervisorDecision {
	action: "allow" | "block" | "modify"
	originalCommand?: string
	modifiedCommand?: string
	reason: string
	riskLevel: "low" | "medium" | "high" | "critical"
	timestamp: number
	sessionId: string
}

export interface SupervisorConfig {
	enabled: boolean
	maxProcessTimeout: number // milliseconds
	unboundedProcessPatterns: string[]
	allowedLongRunningCommands: string[]
	blockServerCommands: boolean
	blockNpmStart: boolean
	logDecisions: boolean
	emergencyKillTimeout: number // milliseconds
}

export interface SupervisorEvents {
	decision: (decision: SupervisorDecision) => void
	processBlocked: (command: string, reason: string) => void
	processModified: (original: string, modified: string, reason: string) => void
	emergencyKill: (pid: number, reason: string) => void
}

/**
 * Autonomous Supervisor Layer
 * 
 * Continuously monitors every message, model invocation, and terminal command
 * across the entire session. Intercepts and blocks unbounded processes to
 * maintain session responsiveness.
 */
export class AutonomousSupervisor extends EventEmitter<SupervisorEvents> {
	private static instance: AutonomousSupervisor | null = null
	private config: SupervisorConfig
	private configManager: SupervisorConfigManager
	private logger: SupervisorLogger
	private sessionId: string
	private decisions: SupervisorDecision[] = []
	private activeProcesses = new Map<number, { command: string; startTime: number; timeout?: NodeJS.Timeout }>()
	private outputChannel: vscode.OutputChannel
	private configChangeListener: vscode.Disposable | null = null

	// Default patterns for unbounded processes
	private readonly DEFAULT_UNBOUNDED_PATTERNS = [
		// Server commands
		"npm start",
		"yarn start",
		"pnpm start",
		"npm run dev",
		"yarn dev",
		"pnpm dev",
		"npm run serve",
		"yarn serve",
		"pnpm serve",
		"python -m http.server",
		"python3 -m http.server",
		"http-server",
		"live-server",
		"serve",
		"nodemon",
		"pm2 start",
		"forever start",
		
		// Development servers
		"webpack-dev-server",
		"vite",
		"next dev",
		"nuxt dev",
		"gatsby develop",
		"hugo server",
		"jekyll serve",
		"rails server",
		"django runserver",
		"flask run",
		"uvicorn",
		"gunicorn",
		
		// Database and services
		"mongod",
		"redis-server",
		"postgres",
		"mysql",
		"elasticsearch",
		"docker-compose up",
		"docker run.*-d",
		
		// Long-running processes
		"tail -f",
		"watch ",
		"while true",
		"for.*;;",
		"sleep [0-9]+[smhd]",
		"ping.*-t",
		"nc.*-l",
		"netcat.*-l",
		
		// Build watchers
		"webpack.*--watch",
		"tsc.*--watch",
		"sass.*--watch",
		"rollup.*--watch",
	]

	private constructor(outputChannel: vscode.OutputChannel) {
		super()
		this.outputChannel = outputChannel
		this.sessionId = `supervisor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
		this.configManager = SupervisorConfigManager.getInstance()
		this.config = this.configManager.loadConfiguration()
		this.logger = new SupervisorLogger(outputChannel, this.sessionId)

		// Listen for configuration changes
		this.configChangeListener = this.configManager.onConfigurationChange((newConfig) => {
			const oldConfig = this.config
			this.config = newConfig
			this.logger.logConfigChange({ old: oldConfig, new: newConfig })
		})

		this.logger.log("info", "config", "Autonomous Supervisor initialized")
	}

	public static getInstance(outputChannel?: vscode.OutputChannel): AutonomousSupervisor {
		if (!AutonomousSupervisor.instance) {
			if (!outputChannel) {
				throw new Error("OutputChannel required for first initialization")
			}
			AutonomousSupervisor.instance = new AutonomousSupervisor(outputChannel)
		}
		return AutonomousSupervisor.instance
	}

	private getDefaultConfig(): SupervisorConfig {
		return {
			enabled: true,
			maxProcessTimeout: 30000, // 30 seconds
			unboundedProcessPatterns: this.DEFAULT_UNBOUNDED_PATTERNS,
			allowedLongRunningCommands: [],
			blockServerCommands: true,
			blockNpmStart: true,
			logDecisions: true,
			emergencyKillTimeout: 5000, // 5 seconds
		}
	}



	/**
	 * Monitor and potentially intercept a webview message
	 */
	public async monitorMessage(message: WebviewMessage): Promise<WebviewMessage | null> {
		if (!this.config.enabled) {
			return message
		}

		// Check for potentially dangerous message types
		if (message.type === "newTask" && message.text) {
			const decision = this.analyzeTaskRequest(message.text)
			if (decision.action === "block") {
				this.recordDecision(decision)
				this.emit("processBlocked", message.text!, decision.reason)
				return null // Block the message
			} else if (decision.action === "modify" && decision.modifiedCommand) {
				this.recordDecision(decision)
				this.emit("processModified", message.text!, decision.modifiedCommand, decision.reason)
				return { ...message, text: decision.modifiedCommand }
			}
		}

		return message
	}

	/**
	 * Monitor and potentially intercept a command execution
	 */
	public async monitorCommand(command: string, task?: Task): Promise<{ allowed: boolean; modifiedCommand?: string; reason?: string }> {
		if (!this.config.enabled) {
			return { allowed: true }
		}

		const decision = this.analyzeCommand(command)
		this.recordDecision(decision)

		switch (decision.action) {
			case "block":
				this.emit("processBlocked", command, decision.reason)
				return { allowed: false, reason: decision.reason }
			
			case "modify":
				this.emit("processModified", command, decision.modifiedCommand!, decision.reason)
				return { allowed: true, modifiedCommand: decision.modifiedCommand, reason: decision.reason }
			
			default:
				return { allowed: true }
		}
	}

	/**
	 * Register a process for monitoring
	 */
	public registerProcess(pid: number, command: string): void {
		if (!this.config.enabled) return

		const processInfo = {
			command,
			startTime: Date.now(),
			timeout: setTimeout(() => {
				this.handleProcessTimeout(pid, command)
			}, this.config.maxProcessTimeout)
		}

		this.activeProcesses.set(pid, processInfo)
		this.logger.logProcessEvent("registered", pid, command)
	}

	/**
	 * Unregister a process when it completes
	 */
	public unregisterProcess(pid: number): void {
		const processInfo = this.activeProcesses.get(pid)
		if (processInfo?.timeout) {
			clearTimeout(processInfo.timeout)
		}
		this.activeProcesses.delete(pid)
		this.logger.logProcessEvent("unregistered", pid)
	}

	private analyzeTaskRequest(taskText: string): SupervisorDecision {
		// Analyze the task text for potentially unbounded operations
		const lowerText = taskText.toLowerCase()
		
		// Check for server-related requests
		if (this.config.blockServerCommands && (
			lowerText.includes("start a server") ||
			lowerText.includes("run the server") ||
			lowerText.includes("launch the app") ||
			lowerText.includes("start the development server")
		)) {
			return {
				action: "modify",
				originalCommand: taskText,
				modifiedCommand: taskText + "\n\nNote: Please use time-limited commands or provide specific endpoints to test instead of starting long-running servers.",
				reason: "Task request suggests starting a long-running server",
				riskLevel: "high",
				timestamp: Date.now(),
				sessionId: this.sessionId
			}
		}

		return {
			action: "allow",
			reason: "Task request appears safe",
			riskLevel: "low",
			timestamp: Date.now(),
			sessionId: this.sessionId
		}
	}

	private analyzeCommand(command: string): SupervisorDecision {
		const trimmedCommand = command.trim().toLowerCase()
		
		// Check against unbounded process patterns
		for (const pattern of this.config.unboundedProcessPatterns) {
			const regex = new RegExp(pattern.toLowerCase(), "i")
			if (regex.test(trimmedCommand)) {
				// Check if it's in the allowed list
				const isAllowed = this.config.allowedLongRunningCommands.some(allowed => 
					trimmedCommand.includes(allowed.toLowerCase())
				)
				
				if (!isAllowed) {
					return {
						action: "block",
						originalCommand: command,
						reason: `Command matches unbounded process pattern: ${pattern}`,
						riskLevel: "critical",
						timestamp: Date.now(),
						sessionId: this.sessionId
					}
				}
			}
		}

		// Provide safe alternatives for common dangerous commands
		if (this.config.blockNpmStart && trimmedCommand.includes("npm start")) {
			return {
				action: "modify",
				originalCommand: command,
				modifiedCommand: "echo 'Supervisor: npm start blocked. Use npm run build or npm test instead for non-blocking operations.'",
				reason: "npm start typically runs indefinitely",
				riskLevel: "high",
				timestamp: Date.now(),
				sessionId: this.sessionId
			}
		}

		return {
			action: "allow",
			reason: "Command appears safe",
			riskLevel: "low",
			timestamp: Date.now(),
			sessionId: this.sessionId
		}
	}

	private handleProcessTimeout(pid: number, command: string): void {
		const processInfo = this.activeProcesses.get(pid)
		if (!processInfo) return

		this.logger.logProcessEvent("timeout", pid, command)

		// Attempt graceful termination first
		try {
			process.kill(pid, "SIGTERM")

			// Set emergency kill timeout
			setTimeout(() => {
				try {
					process.kill(pid, "SIGKILL")
					this.emit("emergencyKill", pid, "Process did not respond to SIGTERM")
					this.logger.logProcessEvent("killed", pid, command)
				} catch (error) {
					this.logger.log("error", "process", `Failed to emergency kill process ${pid}: ${error}`)
				}
			}, this.config.emergencyKillTimeout)

		} catch (error) {
			this.logger.log("error", "process", `Failed to terminate process ${pid}: ${error}`)
		}

		this.unregisterProcess(pid)
	}

	private recordDecision(decision: SupervisorDecision): void {
		const startTime = Date.now()
		this.decisions.push(decision)
		const decisionTime = Date.now() - startTime

		// Log decision with timing
		this.logger.logDecision(decision, decisionTime)

		this.emit("decision", decision)

		// Keep only last 1000 decisions to prevent memory leaks
		if (this.decisions.length > 1000) {
			this.decisions = this.decisions.slice(-1000)
		}
	}



	/**
	 * Get supervisor statistics
	 */
	public getStats(): {
		sessionId: string
		decisionsCount: number
		blockedCommands: number
		modifiedCommands: number
		activeProcesses: number
		uptime: number
	} {
		const startTime = parseInt(this.sessionId.split("-")[1])
		return {
			sessionId: this.sessionId,
			decisionsCount: this.decisions.length,
			blockedCommands: this.decisions.filter(d => d.action === "block").length,
			modifiedCommands: this.decisions.filter(d => d.action === "modify").length,
			activeProcesses: this.activeProcesses.size,
			uptime: Date.now() - startTime
		}
	}

	/**
	 * Update configuration
	 */
	public async updateConfig(newConfig: Partial<SupervisorConfig>): Promise<void> {
		await this.configManager.updateConfiguration(newConfig)
		// Configuration will be updated automatically via the change listener
	}

	/**
	 * Get recent decisions
	 */
	public getRecentDecisions(limit: number = 50): SupervisorDecision[] {
		return this.decisions.slice(-limit)
	}

	/**
	 * Get performance metrics
	 */
	public getPerformanceMetrics() {
		return this.logger.getPerformanceMetrics()
	}

	/**
	 * Get security metrics
	 */
	public getSecurityMetrics() {
		return this.logger.getSecurityMetrics()
	}

	/**
	 * Get recent logs
	 */
	public getRecentLogs(limit: number = 100, level?: "debug" | "info" | "warn" | "error", category?: string) {
		return this.logger.getRecentLogs(limit, level, category)
	}

	/**
	 * Generate summary report
	 */
	public generateSummaryReport(): string {
		return this.logger.generateSummaryReport()
	}

	/**
	 * Export logs
	 */
	public exportLogs(): string {
		return this.logger.exportLogs()
	}

	/**
	 * Cleanup resources
	 */
	public dispose(): void {
		// Clear all active process timeouts
		for (const [pid, processInfo] of this.activeProcesses) {
			if (processInfo.timeout) {
				clearTimeout(processInfo.timeout)
			}
		}
		this.activeProcesses.clear()
		this.decisions = []

		// Dispose configuration change listener
		if (this.configChangeListener) {
			this.configChangeListener.dispose()
			this.configChangeListener = null
		}

		// Dispose logger
		this.logger.log("info", "config", "Supervisor disposed")
		this.logger.dispose()

		AutonomousSupervisor.instance = null
	}
}
