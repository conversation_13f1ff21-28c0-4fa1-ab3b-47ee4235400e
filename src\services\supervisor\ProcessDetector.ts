import * as path from "path"

export interface ProcessAnalysis {
	isUnbounded: boolean
	riskLevel: "low" | "medium" | "high" | "critical"
	category: ProcessCategory
	confidence: number // 0-1
	reasons: string[]
	suggestedAlternative?: string
}

export enum ProcessCategory {
	SAFE = "safe",
	BUILD_TOOL = "build_tool",
	TEST_RUNNER = "test_runner",
	SERVER = "server",
	DATABASE = "database",
	WATCHER = "watcher",
	DAEMON = "daemon",
	INTERACTIVE = "interactive",
	NETWORK = "network",
	SYSTEM = "system"
}

export interface DetectionRule {
	pattern: RegExp
	category: ProcessCategory
	riskLevel: "low" | "medium" | "high" | "critical"
	isUnbounded: boolean
	confidence: number
	reason: string
	suggestedAlternative?: string
}

/**
 * Advanced Process Detection Engine
 * 
 * Uses multiple detection strategies to identify potentially unbounded processes:
 * 1. Pattern matching against known unbounded commands
 * 2. Semantic analysis of command structure
 * 3. Context-aware risk assessment
 * 4. Machine learning-inspired scoring
 */
export class ProcessDetector {
	private detectionRules: DetectionRule[]
	private contextualKeywords: Map<string, number> // keyword -> risk weight

	constructor() {
		this.detectionRules = this.initializeDetectionRules()
		this.contextualKeywords = this.initializeContextualKeywords()
	}

	/**
	 * Analyze a command to determine if it's potentially unbounded
	 */
	public analyzeCommand(command: string, workingDir?: string): ProcessAnalysis {
		const normalizedCommand = this.normalizeCommand(command)
		const tokens = this.tokenizeCommand(normalizedCommand)
		
		// Apply multiple detection strategies
		const patternAnalysis = this.applyPatternMatching(normalizedCommand)
		const semanticAnalysis = this.applySemanticAnalysis(tokens)
		const contextualAnalysis = this.applyContextualAnalysis(normalizedCommand, workingDir)
		const structuralAnalysis = this.applyStructuralAnalysis(normalizedCommand)
		
		// Combine analyses using weighted scoring
		return this.combineAnalyses([
			patternAnalysis,
			semanticAnalysis,
			contextualAnalysis,
			structuralAnalysis
		])
	}

	private initializeDetectionRules(): DetectionRule[] {
		return [
			// Server commands - Critical risk
			{
				pattern: /^(npm|yarn|pnpm)\s+(start|run\s+dev|run\s+serve)(\s|$)/i,
				category: ProcessCategory.SERVER,
				riskLevel: "critical",
				isUnbounded: true,
				confidence: 0.95,
				reason: "Package manager dev server command",
				suggestedAlternative: "npm run build && npm test"
			},
			{
				pattern: /^(python|python3)\s+-m\s+http\.server/i,
				category: ProcessCategory.SERVER,
				riskLevel: "critical",
				isUnbounded: true,
				confidence: 0.9,
				reason: "Python HTTP server",
				suggestedAlternative: "curl http://localhost:8000 # to test existing server"
			},
			{
				pattern: /^(nodemon|pm2\s+start|forever\s+start)/i,
				category: ProcessCategory.DAEMON,
				riskLevel: "critical",
				isUnbounded: true,
				confidence: 0.95,
				reason: "Process manager daemon",
				suggestedAlternative: "node script.js # run once instead"
			},

			// Development servers - High risk
			{
				pattern: /^(webpack-dev-server|vite|next\s+dev|nuxt\s+dev)/i,
				category: ProcessCategory.SERVER,
				riskLevel: "high",
				isUnbounded: true,
				confidence: 0.9,
				reason: "Development server",
				suggestedAlternative: "npm run build # build for production"
			},
			{
				pattern: /^(gatsby\s+develop|hugo\s+server|jekyll\s+serve)/i,
				category: ProcessCategory.SERVER,
				riskLevel: "high",
				isUnbounded: true,
				confidence: 0.85,
				reason: "Static site development server",
				suggestedAlternative: "gatsby build # build static site"
			},

			// Database and services - High risk
			{
				pattern: /^(mongod|redis-server|postgres|mysql|elasticsearch)/i,
				category: ProcessCategory.DATABASE,
				riskLevel: "high",
				isUnbounded: true,
				confidence: 0.9,
				reason: "Database server",
				suggestedAlternative: "# Use existing database connection or docker container"
			},
			{
				pattern: /^docker-compose\s+up(\s|$)/i,
				category: ProcessCategory.SYSTEM,
				riskLevel: "high",
				isUnbounded: true,
				confidence: 0.85,
				reason: "Docker Compose services",
				suggestedAlternative: "docker-compose up -d # run in background, or docker-compose build"
			},

			// Watchers and monitors - Medium to High risk
			{
				pattern: /^tail\s+-f/i,
				category: ProcessCategory.WATCHER,
				riskLevel: "medium",
				isUnbounded: true,
				confidence: 0.8,
				reason: "File tail watcher",
				suggestedAlternative: "head -n 50 filename # show recent lines"
			},
			{
				pattern: /^watch\s+/i,
				category: ProcessCategory.WATCHER,
				riskLevel: "medium",
				isUnbounded: true,
				confidence: 0.75,
				reason: "Command watcher",
				suggestedAlternative: "# Run command once instead of watching"
			},
			{
				pattern: /(webpack|tsc|sass|rollup).*--watch/i,
				category: ProcessCategory.WATCHER,
				riskLevel: "medium",
				isUnbounded: true,
				confidence: 0.8,
				reason: "Build tool watcher",
				suggestedAlternative: "# Run build once without --watch flag"
			},

			// Interactive and long-running processes
			{
				pattern: /^(vim|nano|emacs|code)\s+/i,
				category: ProcessCategory.INTERACTIVE,
				riskLevel: "medium",
				isUnbounded: true,
				confidence: 0.7,
				reason: "Interactive editor",
				suggestedAlternative: "cat filename # view file content"
			},
			{
				pattern: /^(ssh|telnet|nc|netcat).*-l/i,
				category: ProcessCategory.NETWORK,
				riskLevel: "high",
				isUnbounded: true,
				confidence: 0.85,
				reason: "Network listener",
				suggestedAlternative: "# Use curl or wget for one-time network requests"
			},

			// Infinite loops and long sleeps
			{
				pattern: /while\s+(true|1|:)/i,
				category: ProcessCategory.SYSTEM,
				riskLevel: "critical",
				isUnbounded: true,
				confidence: 0.95,
				reason: "Infinite loop",
				suggestedAlternative: "# Replace with finite loop or single execution"
			},
			{
				pattern: /for\s*\(\s*;\s*;\s*\)/i,
				category: ProcessCategory.SYSTEM,
				riskLevel: "critical",
				isUnbounded: true,
				confidence: 0.95,
				reason: "Infinite for loop",
				suggestedAlternative: "# Use finite loop with proper condition"
			},
			{
				pattern: /sleep\s+([0-9]+[smhd]|[0-9]{3,})/i,
				category: ProcessCategory.SYSTEM,
				riskLevel: "medium",
				isUnbounded: true,
				confidence: 0.7,
				reason: "Long sleep command",
				suggestedAlternative: "# Use shorter sleep or remove sleep entirely"
			},

			// Safe commands - Low risk
			{
				pattern: /^(ls|dir|cat|echo|pwd|whoami|date|which|where)/i,
				category: ProcessCategory.SAFE,
				riskLevel: "low",
				isUnbounded: false,
				confidence: 0.9,
				reason: "Safe system command"
			},
			{
				pattern: /^(git|svn|hg)\s+/i,
				category: ProcessCategory.SAFE,
				riskLevel: "low",
				isUnbounded: false,
				confidence: 0.85,
				reason: "Version control command"
			},
			{
				pattern: /^(npm|yarn|pnpm)\s+(install|ci|test|run\s+test|run\s+build|run\s+lint)/i,
				category: ProcessCategory.BUILD_TOOL,
				riskLevel: "low",
				isUnbounded: false,
				confidence: 0.8,
				reason: "Package manager build/test command"
			}
		]
	}

	private initializeContextualKeywords(): Map<string, number> {
		return new Map([
			// High-risk keywords
			["server", 0.8],
			["daemon", 0.9],
			["service", 0.7],
			["listen", 0.8],
			["watch", 0.6],
			["monitor", 0.6],
			["infinite", 0.95],
			["forever", 0.9],
			["continuous", 0.7],
			
			// Medium-risk keywords
			["dev", 0.5],
			["development", 0.5],
			["hot", 0.4],
			["reload", 0.4],
			["live", 0.5],
			
			// Low-risk keywords
			["build", -0.3],
			["test", -0.4],
			["lint", -0.3],
			["compile", -0.2],
			["install", -0.4]
		])
	}

	private normalizeCommand(command: string): string {
		return command.trim().toLowerCase()
	}

	private tokenizeCommand(command: string): string[] {
		return command.split(/\s+/).filter(token => token.length > 0)
	}

	private applyPatternMatching(command: string): ProcessAnalysis {
		let bestMatch: DetectionRule | null = null
		let highestConfidence = 0

		for (const rule of this.detectionRules) {
			if (rule.pattern.test(command)) {
				if (rule.confidence > highestConfidence) {
					bestMatch = rule
					highestConfidence = rule.confidence
				}
			}
		}

		if (bestMatch) {
			return {
				isUnbounded: bestMatch.isUnbounded,
				riskLevel: bestMatch.riskLevel,
				category: bestMatch.category,
				confidence: bestMatch.confidence,
				reasons: [bestMatch.reason],
				suggestedAlternative: bestMatch.suggestedAlternative
			}
		}

		return {
			isUnbounded: false,
			riskLevel: "low",
			category: ProcessCategory.SAFE,
			confidence: 0.1,
			reasons: ["No pattern match found"]
		}
	}

	private applySemanticAnalysis(tokens: string[]): ProcessAnalysis {
		let riskScore = 0
		const reasons: string[] = []

		// Check for semantic indicators
		for (const token of tokens) {
			const weight = this.contextualKeywords.get(token) || 0
			riskScore += weight
			
			if (Math.abs(weight) > 0.5) {
				reasons.push(`Semantic indicator: ${token} (weight: ${weight})`)
			}
		}

		// Normalize risk score to 0-1 range
		const normalizedScore = Math.max(0, Math.min(1, (riskScore + 1) / 2))
		
		let riskLevel: "low" | "medium" | "high" | "critical"
		if (normalizedScore > 0.8) riskLevel = "critical"
		else if (normalizedScore > 0.6) riskLevel = "high"
		else if (normalizedScore > 0.4) riskLevel = "medium"
		else riskLevel = "low"

		return {
			isUnbounded: normalizedScore > 0.6,
			riskLevel,
			category: ProcessCategory.SYSTEM,
			confidence: Math.abs(normalizedScore - 0.5) * 2, // Higher confidence for extreme scores
			reasons
		}
	}

	private applyContextualAnalysis(command: string, workingDir?: string): ProcessAnalysis {
		const reasons: string[] = []
		let riskScore = 0

		// Check for package.json context
		if (workingDir && command.includes("npm")) {
			try {
				const packageJsonPath = path.join(workingDir, "package.json")
				// In a real implementation, you'd read the package.json
				// For now, we'll use heuristics
				if (command.includes("start")) {
					riskScore += 0.7
					reasons.push("npm start in project directory")
				}
			} catch (error) {
				// Ignore file system errors
			}
		}

		// Check for port indicators
		if (/:\d{4,5}/.test(command)) {
			riskScore += 0.5
			reasons.push("Command contains port number")
		}

		// Check for background process indicators
		if (command.includes("&") || command.includes("nohup")) {
			riskScore += 0.6
			reasons.push("Background process indicator")
		}

		const normalizedScore = Math.min(1, riskScore)
		
		return {
			isUnbounded: normalizedScore > 0.5,
			riskLevel: normalizedScore > 0.7 ? "high" : normalizedScore > 0.4 ? "medium" : "low",
			category: ProcessCategory.SYSTEM,
			confidence: normalizedScore,
			reasons
		}
	}

	private applyStructuralAnalysis(command: string): ProcessAnalysis {
		const reasons: string[] = []
		let riskScore = 0

		// Check command length (very long commands might be complex)
		if (command.length > 200) {
			riskScore += 0.2
			reasons.push("Very long command")
		}

		// Check for pipes and redirections that might indicate complex processing
		const pipeCount = (command.match(/\|/g) || []).length
		if (pipeCount > 3) {
			riskScore += 0.3
			reasons.push("Complex pipe chain")
		}

		// Check for multiple commands (semicolons, &&, ||)
		const commandSeparators = (command.match(/[;&]|\|\||&&/g) || []).length
		if (commandSeparators > 2) {
			riskScore += 0.4
			reasons.push("Multiple chained commands")
		}

		// Check for shell scripting constructs
		if (/\b(if|then|else|fi|case|esac|function)\b/.test(command)) {
			riskScore += 0.5
			reasons.push("Shell scripting constructs")
		}

		return {
			isUnbounded: riskScore > 0.6,
			riskLevel: riskScore > 0.7 ? "high" : riskScore > 0.4 ? "medium" : "low",
			category: ProcessCategory.SYSTEM,
			confidence: Math.min(1, riskScore),
			reasons
		}
	}

	private combineAnalyses(analyses: ProcessAnalysis[]): ProcessAnalysis {
		// Weighted combination of analyses
		const weights = [0.4, 0.3, 0.2, 0.1] // Pattern matching gets highest weight
		
		let combinedRiskScore = 0
		let combinedConfidence = 0
		let isUnbounded = false
		const allReasons: string[] = []
		let highestRiskLevel: "low" | "medium" | "high" | "critical" = "low"
		let primaryCategory = ProcessCategory.SAFE
		let bestAlternative: string | undefined

		for (let i = 0; i < analyses.length; i++) {
			const analysis = analyses[i]
			const weight = weights[i] || 0.1

			// Convert risk level to numeric score
			const riskLevelScore = this.riskLevelToScore(analysis.riskLevel)
			combinedRiskScore += riskLevelScore * weight * analysis.confidence
			combinedConfidence += analysis.confidence * weight

			if (analysis.isUnbounded) {
				isUnbounded = true
			}

			if (this.riskLevelToScore(analysis.riskLevel) > this.riskLevelToScore(highestRiskLevel)) {
				highestRiskLevel = analysis.riskLevel
				primaryCategory = analysis.category
				bestAlternative = analysis.suggestedAlternative
			}

			allReasons.push(...analysis.reasons)
		}

		// Normalize combined scores
		combinedRiskScore = Math.min(1, combinedRiskScore)
		combinedConfidence = Math.min(1, combinedConfidence)

		// Determine final risk level from combined score
		let finalRiskLevel: "low" | "medium" | "high" | "critical"
		if (combinedRiskScore > 0.8) finalRiskLevel = "critical"
		else if (combinedRiskScore > 0.6) finalRiskLevel = "high"
		else if (combinedRiskScore > 0.4) finalRiskLevel = "medium"
		else finalRiskLevel = "low"

		return {
			isUnbounded: isUnbounded || combinedRiskScore > 0.6,
			riskLevel: finalRiskLevel,
			category: primaryCategory,
			confidence: combinedConfidence,
			reasons: [...new Set(allReasons)], // Remove duplicates
			suggestedAlternative: bestAlternative
		}
	}

	private riskLevelToScore(riskLevel: "low" | "medium" | "high" | "critical"): number {
		switch (riskLevel) {
			case "low": return 0.2
			case "medium": return 0.5
			case "high": return 0.8
			case "critical": return 1.0
		}
	}
}
