{"name": "kilo-code", "displayName": "%extension.displayName%", "description": "%extension.description%", "publisher": "kilocode", "version": "4.74.0", "icon": "assets/icons/logo-outline-black.png", "galleryBanner": {"color": "#FFFFFF", "theme": "light"}, "engines": {"vscode": "^1.84.0", "node": "20.19.2"}, "author": {"name": "Kilo Code"}, "repository": {"type": "git", "url": "https://github.com/Kilo-Org/kilocode"}, "homepage": "https://kilocode.ai", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["cline", "claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama", "Kilo Code", "kilocode", "roo code", "roocode"], "activationEvents": ["onLanguage", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"walkthroughs": [{"id": "kiloCodeWalkthrough", "title": "5 ways Kilo Code helps you code", "description": "You now have a personal AI coding assistant.", "steps": [{"id": "welcome", "title": "Write code for you", "description": "Describe what you want to build. Kilo Code will write it from scratch, generate the files, and run them for you.", "media": {"markdown": "./dist/walkthrough/step1.md"}}, {"id": "getting-started", "title": "Understand your codebase", "description": "Need clarity on how something works? Ask <PERSON><PERSON> about any part of your files and get a clear explanation.", "media": {"markdown": "./dist/walkthrough/step2.md"}, "content": {"path": "./dist/walkthrough/step2.md"}}, {"id": "modes", "title": "Get things working again", "description": "Stuck on an error? Kilo Code can find the issue, fix it, and get your code running.", "media": {"markdown": "./dist/walkthrough/step3.md"}}, {"id": "code-actions", "title": "Plan your logic", "description": "Not sure where to start? Map out your logic and structure before writing a single line of code.", "media": {"markdown": "./dist/walkthrough/step4.md"}}, {"id": "advanced-features", "title": "And more", "description": "Improve your prompt, add context, or create your own custom modes.", "media": {"markdown": "./dist/walkthrough/step5.md"}}]}], "viewsContainers": {"activitybar": [{"id": "kilo-code-ActivityBar", "title": "%views.activitybar.title%", "icon": "assets/icons/kilo.png", "darkIcon": "assets/icons/kilo-dark.png", "when": "isMac"}]}, "views": {"kilo-code-ActivityBar": [{"type": "webview", "id": "kilo-code.SidebarProvider", "name": "%views.sidebar.name%"}]}, "commands": [{"command": "kilo-code.plusButtonClicked", "title": "%command.newTask.title%", "icon": "$(add)"}, {"command": "kilo-code.mcpButtonClicked", "title": "%command.mcpServers.title%", "icon": "$(server)"}, {"command": "kilo-code.importSettings", "title": "%command.importSettings.title%", "category": "%configuration.title%"}, {"command": "kilo-code.exportSettings", "title": "Export Settings", "category": "%configuration.title%"}, {"command": "kilo-code.testSemanticSearch", "title": "Test Semantic Search", "category": "%configuration.title%"}, {"command": "kilo-code.promptsButtonClicked", "title": "%command.prompts.title%", "icon": "$(organization)"}, {"command": "kilo-code.historyButtonClicked", "title": "%command.history.title%", "icon": "$(history)"}, {"command": "kilo-code.marketplaceButtonClicked", "title": "%command.marketplace.title%", "icon": "$(extensions)"}, {"command": "kilo-code.popoutButtonClicked", "title": "%command.openInEditor.title%", "icon": "$(link-external)"}, {"command": "kilo-code.settingsButtonClicked", "title": "%command.settings.title%", "icon": "$(settings-gear)"}, {"command": "kilo-code.helpButtonClicked", "title": "%command.documentation.title%", "icon": "$(question)"}, {"command": "kilo-code.openInNewTab", "title": "%command.openInNewTab.title%", "category": "%configuration.title%"}, {"command": "kilo-code.explainCode", "title": "%command.explainCode.title%", "category": "%configuration.title%"}, {"command": "kilo-code.fixCode", "title": "%command.fixCode.title%", "category": "%configuration.title%"}, {"command": "kilo-code.improveCode", "title": "%command.improveCode.title%", "category": "%configuration.title%"}, {"command": "kilo-code.addToContext", "title": "%command.addToContext.title%", "category": "%configuration.title%"}, {"command": "kilo-code.newTask", "title": "%command.newTask.title%", "category": "%configuration.title%"}, {"command": "kilo-code.terminalAddToContext", "title": "%command.terminal.addToContext.title%", "category": "%extension.displayName%"}, {"command": "kilo-code.terminalFixCommand", "title": "%command.terminal.fixCommand.title%", "category": "%extension.displayName%"}, {"command": "kilo-code.terminalExplainCommand", "title": "%command.terminal.explainCommand.title%", "category": "%configuration.title%"}, {"command": "kilo-code.generateTerminalCommand", "title": "%command.terminal.generateCommand.title%", "category": "%configuration.title%"}, {"command": "kilo-code.setCustomStoragePath", "title": "%command.setCustomStoragePath.title%", "category": "%configuration.title%"}, {"command": "kilo-code.focusChatInput", "title": "%command.focusInput.title%", "category": "%configuration.title%"}, {"command": "kilo-code.acceptInput", "title": "%command.acceptInput.title%", "category": "%configuration.title%"}, {"command": "kilo-code.profileButtonClicked", "title": "%command.profile.title%", "icon": "$(account)", "category": "%configuration.title%"}, {"command": "kilo-code.generateCommitMessage", "title": "%command.generateCommitMessage.title%", "icon": {"light": "assets/icons/kilo-light.svg", "dark": "assets/icons/kilo-dark.svg"}, "category": "%configuration.title%"}, {"command": "kilo-code.ghost.generateSuggestions", "title": "%ghost.commands.generateSuggestions%", "category": "%configuration.title%"}, {"command": "kilo-code.ghost.cancelSuggestions", "title": "%ghost.commands.cancelSuggestions%", "category": "%configuration.title%"}, {"command": "kilo-code.ghost.applyCurrentSuggestions", "title": "%ghost.commands.applyCurrentSuggestion%", "category": "%configuration.title%"}, {"command": "kilo-code.ghost.applyAllSuggestions", "title": "%ghost.commands.applyAllSuggestions%", "category": "%configuration.title%"}, {"command": "kilo-code.ghost.promptCodeSuggestion", "title": "%ghost.commands.promptCodeSuggestion%", "category": "%configuration.title%"}, {"command": "kilo-code.ghost.goToNextSuggestion", "title": "%ghost.commands.goToNextSuggestion%", "category": "%configuration.title%"}, {"command": "kilo-code.ghost.goToPreviousSuggestion", "title": "%ghost.commands.goToPreviousSuggestion%", "category": "%configuration.title%"}], "keybindings": [{"command": "kilo-code.focusChatInput", "key": "ctrl+shift+a", "mac": "cmd+shift+a", "when": "true"}, {"command": "editor.action.inlineSuggest.commit", "key": "tab", "when": "inlineSuggestionVisible && editorTextFocus && !editorTabMovesFocus && !inSnippetMode && !suggestWidgetVisible && editorLangId != 'markdown'"}, {"command": "kilo-code.ghost.applyAllSuggestions", "key": "shift+tab", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.hasSuggestions"}, {"command": "kilo-code.ghost.applyCurrentSuggestions", "key": "tab", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.hasSuggestions"}, {"command": "kilo-code.ghost.cancelSuggestions", "key": "escape", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.hasSuggestions"}, {"command": "kilo-code.ghost.goToNextSuggestion", "key": "down", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.hasSuggestions"}, {"command": "kilo-code.ghost.goToPreviousSuggestion", "key": "up", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.hasSuggestions"}, {"command": "kilo-code.ghost.promptCodeSuggestion", "key": "ctrl+i", "mac": "cmd+i", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.enableQuickInlineTaskKeybinding && !github.copilot.completions.enabled"}, {"command": "kilo-code.ghost.generateSuggestions", "key": "ctrl+l", "mac": "cmd+l", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.enableAutoInlineTaskKeybinding && !github.copilot.completions.enabled"}, {"command": "kilo-code.ghost.showIncompatibilityExtensionPopup", "key": "ctrl+l", "mac": "cmd+l", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.enableAutoInlineTaskKeybinding && github.copilot.completions.enabled"}, {"command": "kilo-code.ghost.showIncompatibilityExtensionPopup", "key": "ctrl+i", "mac": "cmd+i", "when": "editorTextFocus && !editorTabMovesFocus && !inSnippetMode && kilocode.ghost.enableQuickInlineTaskKeybinding && github.copilot.completions.enabled"}, {"command": "kilo-code.generateTerminalCommand", "key": "ctrl+shift+g", "mac": "cmd+shift+g"}], "menus": {"editor/context": [{"submenu": "kilo-code.contextMenu", "group": "navigation"}], "kilo-code.contextMenu": [{"command": "kilo-code.explainCode", "group": "1_actions@1"}, {"command": "kilo-code.fixCode", "group": "1_actions@2"}, {"command": "kilo-code.improveCode", "group": "1_actions@3"}, {"command": "kilo-code.addToContext", "group": "1_actions@4"}], "terminal/context": [{"submenu": "kilo-code.terminalMenu", "group": "navigation"}], "kilo-code.terminalMenu": [{"command": "kilo-code.terminalAddToContext", "group": "1_actions@1"}, {"command": "kilo-code.terminalFixCommand", "group": "1_actions@2"}, {"command": "kilo-code.terminalExplainCommand", "group": "1_actions@3"}], "view/title": [{"command": "kilo-code.plusButtonClicked", "group": "navigation@1", "when": "view == kilo-code.SidebarProvider"}, {"command": "kilo-code.mcpButtonClicked", "group": "navigation@2", "when": "view == kilo-code.SidebarProvider"}, {"command": "kilo-code.historyButtonClicked", "group": "navigation@3", "when": "view == kilo-code.SidebarProvider"}, {"command": "kilo-code.profileButtonClicked", "group": "navigation@4", "when": "view == kilo-code.SidebarProvider"}, {"command": "kilo-code.settingsButtonClicked", "group": "navigation@5", "when": "view == kilo-code.SidebarProvider"}, {"command": "kilo-code.popoutButtonClicked", "group": "navigation@6", "when": "view == kilo-code.SidebarProvider"}, {"command": "kilo-code.helpButtonClicked", "group": "navigation@8", "when": "false && view == kilo-code.SidebarProvider"}], "editor/title": [{"command": "kilo-code.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == kilo-code.TabPanelProvider"}, {"command": "kilo-code.mcpButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == kilo-code.TabPanelProvider"}, {"command": "kilo-code.historyButtonClicked", "group": "navigation@3", "when": "activeWebviewPanelId == kilo-code.TabPanelProvider"}, {"command": "kilo-code.popoutButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == kilo-code.TabPanelProvider"}, {"command": "kilo-code.settingsButtonClicked", "group": "navigation@5", "when": "activeWebviewPanelId == kilo-code.TabPanelProvider"}, {"command": "kilo-code.helpButtonClicked", "group": "navigation@6", "when": "activeWebviewPanelId == kilo-code.TabPanelProvider"}], "scm/input": [{"command": "kilo-code.generateCommitMessage", "group": "navigation"}], "scm/title": [{"command": "kilo-code.generateCommitMessage", "when": "scmProvider == git", "group": "navigation"}]}, "submenus": [{"id": "kilo-code.contextMenu", "label": "%views.contextMenu.label%"}, {"id": "kilo-code.terminalMenu", "label": "%views.terminalMenu.label%"}], "configuration": {"title": "%configuration.title%", "properties": {"kilo-code.allowedCommands": {"type": "array", "items": {"type": "string"}, "default": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "description": "%commands.allowedCommands.description%"}, "kilo-code.deniedCommands": {"type": "array", "items": {"type": "string"}, "default": [], "description": "%commands.deniedCommands.description%"}, "kilo-code.commandExecutionTimeout": {"type": "number", "default": 0, "minimum": 0, "maximum": 600, "description": "%commands.commandExecutionTimeout.description%"}, "kilo-code.vsCodeLmModelSelector": {"type": "object", "properties": {"vendor": {"type": "string", "description": "%settings.vsCodeLmModelSelector.vendor.description%"}, "family": {"type": "string", "description": "%settings.vsCodeLmModelSelector.family.description%"}}, "description": "%settings.vsCodeLmModelSelector.description%"}, "kilo-code.customStoragePath": {"type": "string", "default": "", "description": "%settings.customStoragePath.description%"}, "kilo-code.enableCodeActions": {"type": "boolean", "default": true, "description": "%settings.enableCodeActions.description%"}, "kilo-code.autoImportSettingsPath": {"type": "string", "default": "", "description": "%settings.autoImportSettingsPath.description%"}, "kilo-code.useAgentRules": {"type": "boolean", "default": true, "description": "%settings.useAgentRules.description%"}, "kilo-code.supervisor.enabled": {"type": "boolean", "default": true, "description": "Enable the Autonomous Supervisor to monitor and control potentially unbounded processes"}, "kilo-code.supervisor.maxProcessTimeout": {"type": "number", "default": 30000, "minimum": 5000, "maximum": 300000, "description": "Maximum time (in milliseconds) a process can run before being terminated by the supervisor"}, "kilo-code.supervisor.unboundedProcessPatterns": {"type": "array", "items": {"type": "string"}, "default": ["npm start", "yarn start", "pnpm start", "npm run dev", "yarn dev", "pnpm dev", "nodemon", "pm2 start", "webpack-dev-server", "vite", "next dev", "tail -f", "watch ", "while true", "docker-compose up"], "description": "Patterns that match potentially unbounded processes that should be blocked or modified"}, "kilo-code.supervisor.allowedLongRunningCommands": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Commands that are allowed to run even if they match unbounded process patterns"}, "kilo-code.supervisor.blockServerCommands": {"type": "boolean", "default": true, "description": "Block commands that start development servers or long-running services"}, "kilo-code.supervisor.blockNpmStart": {"type": "boolean", "default": true, "description": "Specifically block 'npm start' commands which typically run indefinitely"}, "kilo-code.supervisor.logDecisions": {"type": "boolean", "default": true, "description": "Log supervisor decisions to the output channel for debugging and monitoring"}, "kilo-code.supervisor.emergencyKillTimeout": {"type": "number", "default": 5000, "minimum": 1000, "maximum": 30000, "description": "Time (in milliseconds) to wait before force-killing a process that doesn't respond to SIGTERM"}}}, "codeActions": [{"languages": ["*"], "providedCodeActionKinds": ["vscode.CodeActionKind.QuickFix"]}]}, "scripts": {"lint": "eslint . --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "pretest": "turbo run bundle --cwd ..", "test": "vitest run", "format": "prettier --write .", "bundle": "node esbuild.mjs", "vscode:prepublish": "pnpm bundle --production", "vsix": "mkdirp ../bin && vsce package --no-dependencies --out ../bin", "publish:marketplace": "vsce publish --no-dependencies && ovsx publish --no-dependencies", "watch:bundle": "pnpm bundle --watch", "watch:tsc": "cd .. && tsc --noEmit --watch --project src/tsconfig.json", "clean": "rimraf README.md CHANGELOG.md LICENSE dist logs mock .turbo"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.22.0", "@anthropic-ai/sdk": "^0.51.0", "@anthropic-ai/vertex-sdk": "^0.11.3", "@aws-sdk/client-bedrock-runtime": "^3.812.0", "@aws-sdk/credential-providers": "^3.806.0", "@cerebras/cerebras_cloud_sdk": "^1.35.0", "@google/genai": "^1.0.0", "@lmstudio/sdk": "^1.1.1", "@mistralai/mistralai": "^1.3.6", "@modelcontextprotocol/sdk": "^1.9.0", "@qdrant/js-client-rest": "^1.14.0", "@roo-code/cloud": "workspace:^", "@roo-code/ipc": "workspace:^", "@roo-code/telemetry": "workspace:^", "@roo-code/types": "workspace:^", "@types/lodash.debounce": "^4.0.9", "@vscode/codicons": "^0.0.36", "async-mutex": "^0.5.0", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "diff-match-patch": "^1.0.5", "exceljs": "^4.4.0", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^5.0.0", "fastest-levenshtein": "^1.0.16", "fuse.js": "^7.1.0", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "google-auth-library": "^9.15.1", "gray-matter": "^4.0.3", "i18next": "^25.0.0", "ignore": "^7.0.3", "isbinaryfile": "^5.0.2", "lodash.debounce": "^4.0.8", "lru-cache": "^11.1.0", "mammoth": "^1.9.1", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-cache": "^5.1.2", "node-ipc": "^12.0.0", "ollama": "^0.5.16", "openai": "^5.0.0", "os-name": "^6.0.0", "p-limit": "^6.2.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pkce-challenge": "^5.0.0", "pretty-bytes": "^7.0.0", "proper-lockfile": "^4.1.2", "ps-tree": "^1.2.0", "puppeteer-chromium-resolver": "^24.0.0", "puppeteer-core": "^23.4.0", "reconnecting-eventsource": "^1.6.4", "sanitize-filename": "^1.6.3", "say": "^0.16.0", "serialize-error": "^12.0.0", "simple-git": "^3.27.0", "sound-play": "^1.1.0", "stream-json": "^1.8.0", "string-similarity": "^4.0.4", "strip-ansi": "^7.1.0", "strip-bom": "^5.0.0", "tiktoken": "^1.0.21", "tmp": "^0.2.3", "tree-sitter-wasms": "^0.1.12", "turndown": "^7.2.0", "undici": "^7.13.0", "uri-js": "^4.4.1", "uuid": "^11.1.0", "vscode-material-icons": "^0.1.1", "web-tree-sitter": "^0.25.6", "workerpool": "^9.2.0", "yaml": "^2.8.0", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/build": "workspace:^", "@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/clone-deep": "^4.0.4", "@types/debug": "^4.1.12", "@types/diff": "^5.2.1", "@types/diff-match-patch": "^1.0.36", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/node-cache": "^4.1.3", "@types/node-ipc": "^9.2.3", "@types/proper-lockfile": "^4.1.4", "@types/ps-tree": "^1.1.6", "@types/stream-json": "^1.7.8", "@types/string-similarity": "^4.0.2", "@types/tmp": "^0.2.6", "@types/turndown": "^5.0.5", "@types/vscode": "^1.84.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "3.3.2", "esbuild": "^0.25.0", "execa": "^9.5.2", "glob": "^11.0.1", "mkdirp": "^3.0.1", "nock": "^14.0.4", "npm-run-all2": "^8.0.1", "ovsx": "0.10.4", "rimraf": "^6.0.1", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "5.8.3", "vitest": "^3.2.3", "zod-to-ts": "^1.2.0"}}