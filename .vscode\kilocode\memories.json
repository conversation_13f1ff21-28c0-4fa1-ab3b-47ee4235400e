{"version": "1.0", "lastUpdated": "2025-08-06T14:07:01.924Z", "memories": [{"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 4, "quotes": "single", "semicolons": false}, "id": "acde326c-82d3-4bce-9ad2-5a8c70b3e131", "createdAt": "2025-08-06T13:07:44.500Z", "lastAccessed": "2025-08-06T13:07:44.500Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript", "framework": "express"}, "importance": 0.7999999999999999, "confidence": 0.7999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "semicolons": false}, "id": "a704c64f-f107-44a5-8ec6-0ee73736be3c", "createdAt": "2025-08-06T13:08:44.714Z", "lastAccessed": "2025-08-06T13:08:44.714Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript", "framework": "react"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 3, "quotes": "double", "semicolons": false}, "id": "ccd55fb6-5d82-4961-8f77-2d434dfdbe21", "createdAt": "2025-08-06T13:09:39.780Z", "lastAccessed": "2025-08-06T13:09:39.780Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 3, "quotes": "single", "semicolons": false}, "id": "80c04ad2-4413-42e7-b56e-3c3fdd51382d", "createdAt": "2025-08-06T13:10:36.666Z", "lastAccessed": "2025-08-06T13:10:36.666Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript", "framework": "vue"}, "importance": 0.7999999999999999, "confidence": 0.7999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 8, "quotes": "double"}, "id": "71f977d3-0daa-482d-86eb-ba2e8f78089c", "createdAt": "2025-08-06T13:13:11.599Z", "lastAccessed": "2025-08-06T13:13:11.599Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript", "framework": "react"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 3, "quotes": "double", "semicolons": false}, "id": "edf3f294-1926-49df-a457-033b3b6871fd", "createdAt": "2025-08-06T13:15:40.844Z", "lastAccessed": "2025-08-06T13:15:40.844Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "double", "semicolons": false}, "id": "c6008a26-f5a5-4ba6-989d-60553f960aa9", "createdAt": "2025-08-06T13:19:08.393Z", "lastAccessed": "2025-08-06T13:19:08.393Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "double", "semicolons": false}, "id": "241e0a1a-f626-44be-8717-2e421c40d69d", "createdAt": "2025-08-06T13:36:15.973Z", "lastAccessed": "2025-08-06T13:36:15.973Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 1, "quotes": "double", "semicolons": false}, "id": "21e9fdbf-acda-43a9-93b0-a130522d230d", "createdAt": "2025-08-06T13:38:37.941Z", "lastAccessed": "2025-08-06T13:38:37.941Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 1, "quotes": "double", "semicolons": false}, "id": "3260b0fe-d687-4b81-a12e-439b3cd1ac54", "createdAt": "2025-08-06T13:41:38.387Z", "lastAccessed": "2025-08-06T13:41:38.387Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 1, "quotes": "double", "semicolons": false}, "id": "2b8ec3fe-1b72-45bd-ac5c-3edf110450cb", "createdAt": "2025-08-06T13:43:33.404Z", "lastAccessed": "2025-08-06T13:43:33.404Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 4, "quotes": "double", "semicolons": false}, "id": "c391dd15-7dcc-4c72-a2da-d08ac318d4dc", "createdAt": "2025-08-06T13:44:29.213Z", "lastAccessed": "2025-08-06T13:44:29.213Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "double", "semicolons": false}, "id": "eaa34d3e-f68a-4e44-817c-abb0fe0a3b68", "createdAt": "2025-08-06T13:46:38.085Z", "lastAccessed": "2025-08-06T13:46:38.085Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "double", "semicolons": false}, "id": "e7d696fb-9538-4889-a204-e29e9208f671", "createdAt": "2025-08-06T13:48:06.509Z", "lastAccessed": "2025-08-06T13:48:06.509Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 1, "quotes": "double", "semicolons": false}, "id": "b336fa0a-9981-4467-bf20-a188d84f66c2", "createdAt": "2025-08-06T13:49:03.693Z", "lastAccessed": "2025-08-06T13:49:03.693Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "single", "semicolons": false}, "id": "afd9e2d8-d08d-40e2-8eab-0a9ee9835058", "createdAt": "2025-08-06T13:50:18.027Z", "lastAccessed": "2025-08-06T13:50:18.027Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 1, "quotes": "double", "semicolons": false}, "id": "a8fd1e43-6117-4dd8-8675-f8b58068cd9e", "createdAt": "2025-08-06T13:51:50.966Z", "lastAccessed": "2025-08-06T13:51:50.966Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 3, "quotes": "single", "semicolons": false}, "id": "8151b868-c3ef-4ae0-8deb-d90fca267202", "createdAt": "2025-08-06T13:52:43.103Z", "lastAccessed": "2025-08-06T13:52:43.103Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "single", "semicolons": false}, "id": "b5510de6-9d56-4415-a898-464414086176", "createdAt": "2025-08-06T13:53:12.197Z", "lastAccessed": "2025-08-06T13:53:12.197Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 1, "quotes": "single", "semicolons": false}, "id": "134f3650-abf7-4d1b-a956-5afe36394e50", "createdAt": "2025-08-06T13:54:18.599Z", "lastAccessed": "2025-08-06T13:54:18.599Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 8, "quotes": "double", "semicolons": false}, "id": "3f2351e3-611e-4a36-817d-084c299ff269", "createdAt": "2025-08-06T13:55:57.949Z", "lastAccessed": "2025-08-06T13:55:57.949Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "single", "semicolons": false}, "id": "428e630d-3b4d-4f20-972e-9d070bd6221a", "createdAt": "2025-08-06T13:57:05.009Z", "lastAccessed": "2025-08-06T13:57:05.009Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "single", "semicolons": false}, "id": "523de7b5-934f-4846-9ded-3b9b64b9d167", "createdAt": "2025-08-06T13:58:27.445Z", "lastAccessed": "2025-08-06T13:58:27.445Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.7999999999999999, "confidence": 0.7999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "semicolons": false}, "id": "c0ef1509-adb6-46b3-b85a-c799c62b12b9", "createdAt": "2025-08-06T13:59:24.438Z", "lastAccessed": "2025-08-06T13:59:24.438Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.7999999999999999, "confidence": 0.7999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "semicolons": false}, "id": "93f00be6-8925-442f-9635-b0c50c1f9e01", "createdAt": "2025-08-06T14:00:16.182Z", "lastAccessed": "2025-08-06T14:00:16.182Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "double", "semicolons": false}, "id": "f0fc2e9d-fb2c-4dd6-a866-eee8d5edb2c3", "createdAt": "2025-08-06T14:00:37.283Z", "lastAccessed": "2025-08-06T14:00:37.283Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "single", "semicolons": false}, "id": "091b3c5e-5024-4029-9f22-6bfb2b28f371", "createdAt": "2025-08-06T14:01:01.754Z", "lastAccessed": "2025-08-06T14:01:01.754Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.7999999999999999, "confidence": 0.7999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "semicolons": false}, "id": "52c40a62-aff2-4651-96b6-9f05f0d1f8f6", "createdAt": "2025-08-06T14:02:58.556Z", "lastAccessed": "2025-08-06T14:02:58.556Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "double", "semicolons": false}, "id": "e8b0f91f-f89d-440d-ba91-28e279b42e44", "createdAt": "2025-08-06T14:03:28.956Z", "lastAccessed": "2025-08-06T14:03:28.956Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 1, "quotes": "double", "semicolons": false}, "id": "cd718e8f-b852-46ea-9698-080a29bb0421", "createdAt": "2025-08-06T14:03:57.357Z", "lastAccessed": "2025-08-06T14:03:57.357Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "single", "semicolons": false}, "id": "ab631d4a-fde0-4cda-ab0c-9b86cbbb65b5", "createdAt": "2025-08-06T14:04:22.032Z", "lastAccessed": "2025-08-06T14:04:22.032Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "double", "semicolons": false}, "id": "fcfd8e37-c4ca-48c3-8f30-a8dcbe5117cf", "createdAt": "2025-08-06T14:05:05.511Z", "lastAccessed": "2025-08-06T14:05:05.511Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "quotes": "double", "semicolons": false}, "id": "9f5c3cd5-d831-4c38-8474-c9bab511d536", "createdAt": "2025-08-06T14:05:46.728Z", "lastAccessed": "2025-08-06T14:05:46.728Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 6, "quotes": "double", "semicolons": false}, "id": "b6e515a2-a50b-4d11-8037-5cdcc0622a57", "createdAt": "2025-08-06T14:06:09.012Z", "lastAccessed": "2025-08-06T14:06:09.012Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.7999999999999999, "confidence": 0.7999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 2, "semicolons": false}, "id": "3d85a7fd-b524-4d5e-8929-a931a259f5d3", "createdAt": "2025-08-06T14:06:32.597Z", "lastAccessed": "2025-08-06T14:06:32.597Z", "accessCount": 0}, {"type": "coding_style", "content": "Coding style preferences for typescript", "context": {"workspace": "current-workspace", "scope": "language", "language": "typescript"}, "importance": 0.8999999999999999, "confidence": 0.8999999999999999, "tags": ["coding-style", "typescript", "formatting"], "metadata": {"language": "typescript"}, "style": {"indentation": "spaces", "indentSize": 6, "quotes": "double", "semicolons": false}, "id": "8596371b-55f2-47bb-b11a-c797234f0e43", "createdAt": "2025-08-06T14:07:01.924Z", "lastAccessed": "2025-08-06T14:07:01.924Z", "accessCount": 0}]}